# Dynamic Hearts - Minecraft Bedrock Add-On

A comprehensive Minecraft Bedrock Edition add-on that implements a progressive heart system where players start with 1 heart and gain additional hearts upon death, up to a maximum of 20 hearts. Players who die at 20 hearts are switched to spectator mode for hardcore-style elimination.

## Features

### Core Mechanics
- **Progressive Heart System**: Players start with exactly 1 heart (2 health points)
- **Death Progression**: Upon death, players respawn with +1 additional heart
- **Maximum Limit**: Players can have up to 20 hearts (40 health points)
- **Hardcore Elimination**: Players who die at 20 hearts are automatically switched to spectator mode
- **Persistent Storage**: Player heart counts are saved using dynamic properties and persist across sessions

### Technical Features
- **Health Monitoring**: Continuous monitoring system prevents players from exceeding their heart limit
- **Anti-Cheat Protection**: Uses `healthComponent.setCurrentValue()` to directly control health and prevent exploitation
- **Multiplayer Support**: Fully functional in multiplayer environments
- **Performance Optimized**: Efficient interval-based monitoring using `system.runInterval` with batch processing
- **Error Handling**: Comprehensive error handling and recovery systems
- **TypeScript**: Full TypeScript implementation with strict type safety

## Installation

1. **Download**: Download the behavior pack from the releases section
2. **Install**: Place the behavior pack in your Minecraft worlds' `behavior_packs` folder
3. **Activate**: Enable the behavior pack in your world settings
4. **Requirements**: Ensure your world has "Holiday Creator Features" experimental toggle enabled

## File Structure

```
src/eabidakmusic/dynamic_hearts/
├── main.ts              # Main entry point and system initialization
├── constants.ts         # System constants and configuration
├── types.ts            # TypeScript type definitions
├── heartManager.ts     # Core heart management system
├── playerManager.ts    # Player-specific operations
├── eventHandlers.ts    # Event handling (spawn, death, join, leave)
├── healthMonitor.ts    # Continuous health monitoring and enforcement
└── README.md           # This documentation
```

## How It Works

### Player Initialization
- New players automatically start with 1 heart when they first join
- Existing players maintain their current heart count when the add-on is activated
- Player data is stored using Minecraft's dynamic properties system

### Death and Respawn Cycle
1. Player dies with current heart count
2. System increases heart count by 1 (up to maximum of 20)
3. Player respawns with new heart count
4. Health is automatically set to match the new heart count

### Health Enforcement
- Continuous monitoring using `system.runInterval` every second (20 ticks) prevents health exploitation
- Instantly sets health to current maximum using `healthComponent.setCurrentValue()` when exceeded
- Direct health control prevents healing beyond designated heart limits through any means

### Elimination System
- Players who die while at 20 hearts are eliminated
- Eliminated players are switched to spectator mode
- Elimination status is persistent across sessions

## Configuration

### System Constants (constants.ts)
```typescript
// Core gameplay settings
MIN_HEARTS: 1,              // Starting hearts
MAX_HEARTS: 20,             // Maximum hearts before elimination
HEALTH_PER_HEART: 2,        // Minecraft standard (2 HP per heart)

// Performance settings
HEALTH_CHECK_INTERVAL: 20,   // Health monitoring frequency (ticks)
MAINTENANCE_INTERVAL: 1200,  // System maintenance frequency (ticks)
SPAWN_DELAY: 40,            // Delay before applying changes (ticks)
```

### Customization
To modify the system behavior:
1. Edit values in `constants.ts`
2. Recompile TypeScript to JavaScript
3. Test thoroughly in a development world

## API Reference

### Core Classes

#### `DynamicHeartsSystem`
Main system coordinator that manages all components.

#### `PlayerManager`
Handles player-specific operations:
- `initializePlayer(player, isFirstJoin)` - Initialize a player in the system
- `increasePlayerHearts(player)` - Increase player hearts on death
- `eliminatePlayer(player)` - Switch player to spectator mode
- `getPlayerData(player)` - Retrieve player heart data

#### `HealthMonitor`
Monitors and enforces health limits:
- `monitorAllPlayers()` - Check all players' health
- `monitorPlayer(player)` - Check specific player's health
- `enforceHealthLimits(monitorData)` - Apply health corrections

#### `EventHandlers`
Manages game event subscriptions:
- Player join/leave events
- Player spawn/death events
- Automatic cleanup and initialization

## Performance

### Optimization Features
- **Batch Processing**: Players processed in batches to prevent lag
- **Efficient Caching**: Player data cached in memory for fast access
- **Smart Monitoring**: Health checks run on proper intervals using `system.runInterval`
- **Performance Metrics**: Built-in performance monitoring and warnings

### Performance Metrics
- Average tick execution time tracking
- Slow operation detection and logging
- Memory usage monitoring
- Automatic performance optimization

## Troubleshooting

### Common Issues

**Players not starting with 1 heart:**
- Ensure the add-on is properly activated
- Check that experimental features are enabled
- Verify the player hasn't been initialized before

**Health not being enforced:**
- Check console for error messages
- Ensure players are in survival/adventure mode
- Verify the health monitoring system is running

**Players not gaining hearts on death:**
- Check that death events are being detected
- Verify player data is being saved properly
- Ensure the player isn't already at maximum hearts

### Debug Mode
Enable debug mode by modifying the system initialization:
```typescript
const dynamicHeartsSystem = new DynamicHeartsSystem(true); // Enable debug
```

Debug mode provides:
- Detailed console logging
- Performance metrics
- Event tracking
- Error stack traces

## Compatibility

### Minecraft Versions
- **Minimum**: Minecraft Bedrock 1.21.80+
- **Recommended**: Latest stable version
- **API Version**: @minecraft/server 1.19.0+

### Multiplayer Support
- Fully compatible with multiplayer worlds
- Supports dedicated servers
- Player data synced across sessions
- No client-side modifications required

## Development

### Building from Source
1. Install Node.js and TypeScript
2. Run `tsc` to compile TypeScript files
3. Copy compiled JavaScript to behavior pack
4. Test in development world

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make changes with proper TypeScript typing
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Credits

- **Authors**: EabidakMusic, Raboy13
- **Version**: 1.0.0
- **Built with**: TypeScript, @minecraft/server API
- **Documentation**: Context7 Minecraft Bedrock API references

## Support

For support, bug reports, or feature requests:
1. Check the troubleshooting section
2. Enable debug mode for detailed logs
3. Report issues with console output
4. Include world settings and add-on configuration
