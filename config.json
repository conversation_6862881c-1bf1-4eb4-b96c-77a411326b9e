{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "Dynamic Hearts", "packs": {"behaviorPack": "./packs/BP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {}, "formatVersion": "1.4.0", "profiles": {"default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}, "build": {"export": {"build": "standard", "readOnly": false, "target": "local"}, "filters": []}}}}