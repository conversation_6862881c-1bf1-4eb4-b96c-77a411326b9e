/**
 * Dynamic Hearts System - Event Handlers
 *
 * Handles all game events related to player spawning, death, and other
 * relevant events for the dynamic hearts system.
 *
 * <AUTHOR> Raboy13
 * @version 1.0.0
 */
import { world, system } from '@minecraft/server';
import { SYSTEM_CONSTANTS, MESSAGES } from './constants.js';
/**
 * Manages all event handling for the dynamic hearts system
 */
export class EventHandlers {
    playerManager;
    debugMode;
    eventSubscriptions = [];
    constructor(playerManager, debugMode = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
    }
    /**
     * Initialize all event handlers
     */
    initialize() {
        try {
            this.setupPlayerJoinHandler();
            this.setupPlayerLeaveHandler();
            this.setupPlayerSpawnHandler();
            this.setupEntityDeathHandler();
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event handlers initialized successfully');
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to initialize event handlers: ${error}`);
            throw error;
        }
    }
    /**
     * Clean up all event subscriptions
     */
    cleanup() {
        try {
            // Unsubscribe from all events
            this.eventSubscriptions.forEach(unsubscribe => {
                try {
                    unsubscribe();
                }
                catch (error) {
                    console.error(`[Dynamic Hearts] Error unsubscribing from event: ${error}`);
                }
            });
            this.eventSubscriptions.length = 0;
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event handlers cleaned up');
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error during event handler cleanup: ${error}`);
        }
    }
    /**
     * Setup player join event handler
     */
    setupPlayerJoinHandler() {
        const unsubscribe = world.afterEvents.playerJoin.subscribe(async (event) => {
            try {
                // Get player by ID since the event only provides playerId
                const players = world.getAllPlayers();
                const player = players.find(p => p.id === event.playerId);
                if (!player) {
                    console.error(`[Dynamic Hearts] Could not find player with ID: ${event.playerId}`);
                    return;
                }
                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${player.name} joined the game`);
                }
                // Initialize player with a delay to ensure they're fully loaded
                system.runTimeout(async () => {
                    try {
                        const result = await this.playerManager.initializePlayer(player, true);
                        if (!result.success) {
                            console.error(`[Dynamic Hearts] Failed to initialize player ${player.name}: ${result.error?.message}`);
                            player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
                        }
                        else if (result.data) {
                            const message = MESSAGES.INIT.PLAYER_SETUP
                                .replace('{hearts}', result.data.hearts.toString());
                            player.sendMessage(message);
                        }
                    }
                    catch (error) {
                        console.error(`[Dynamic Hearts] Error in delayed player initialization: ${error}`);
                    }
                }, SYSTEM_CONSTANTS.SPAWN_DELAY);
            }
            catch (error) {
                console.error(`[Dynamic Hearts] Error in player join handler: ${error}`);
            }
        });
        // Store a simple cleanup function since we can't properly unsubscribe
        this.eventSubscriptions.push(() => {
            // Event subscriptions in Minecraft Bedrock cannot be easily unsubscribed
            // This is a placeholder for cleanup
        });
    }
    /**
     * Setup player leave event handler
     */
    setupPlayerLeaveHandler() {
        world.afterEvents.playerLeave.subscribe((event) => {
            try {
                const playerId = event.playerId;
                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${playerId} left the game`);
                }
                // Clear player from cache to free memory
                this.playerManager.clearPlayerCache(playerId);
            }
            catch (error) {
                console.error(`[Dynamic Hearts] Error in player leave handler: ${error}`);
            }
        });
        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }
    /**
     * Setup player spawn event handler
     */
    setupPlayerSpawnHandler() {
        world.afterEvents.playerSpawn.subscribe(async (event) => {
            try {
                const player = event.player;
                const initialSpawn = event.initialSpawn;
                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${player.name} spawned (initial: ${initialSpawn})`);
                }
                // Handle spawn with a delay to ensure player is fully loaded
                system.runTimeout(async () => {
                    try {
                        await this.handlePlayerSpawn(player, initialSpawn);
                    }
                    catch (error) {
                        console.error(`[Dynamic Hearts] Error in delayed spawn handler: ${error}`);
                    }
                }, SYSTEM_CONSTANTS.SPAWN_DELAY);
            }
            catch (error) {
                console.error(`[Dynamic Hearts] Error in player spawn handler: ${error}`);
            }
        });
        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }
    /**
     * Setup entity death event handler (for players)
     */
    setupEntityDeathHandler() {
        world.afterEvents.entityDie.subscribe(async (event) => {
            try {
                const entity = event.deadEntity;
                // Check if the dead entity is a player
                if (entity.typeId === 'minecraft:player') {
                    const player = entity;
                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Player ${player.name} died`);
                    }
                    await this.handlePlayerDeath(player);
                }
            }
            catch (error) {
                console.error(`[Dynamic Hearts] Error in entity death handler: ${error}`);
            }
        });
        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }
    /**
     * Handle player spawn event
     */
    async handlePlayerSpawn(player, initialSpawn) {
        try {
            // Get or initialize player data
            let result;
            if (initialSpawn) {
                result = await this.playerManager.initializePlayer(player, true);
            }
            else {
                // For respawns, just ensure player is initialized
                result = await this.playerManager.initializePlayer(player, false);
                // Set health to match current heart count
                if (result.success && result.data) {
                    await this.playerManager.setPlayerHealth(player, result.data.hearts);
                }
            }
            if (!result.success) {
                console.error(`[Dynamic Hearts] Failed to handle player spawn for ${player.name}: ${result.error?.message}`);
                player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
                return;
            }
            // Create spawn event data
            const spawnEventData = {
                player,
                initialSpawn,
                hearts: result.data?.hearts ?? SYSTEM_CONSTANTS.MIN_HEARTS,
                timestamp: Date.now()
            };
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} spawn handled - Hearts: ${spawnEventData.hearts}`);
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error handling player spawn: ${error}`);
            player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
        }
    }
    /**
     * Handle player death event
     */
    async handlePlayerDeath(player) {
        try {
            // Get current player data
            const playerData = await this.playerManager.getPlayerData(player);
            if (!playerData) {
                console.error(`[Dynamic Hearts] No player data found for ${player.name} on death`);
                return;
            }
            const heartsBeforeDeath = playerData.hearts;
            // Increase hearts for next life (or eliminate if at max)
            const result = await this.playerManager.increasePlayerHearts(player);
            if (!result.success) {
                console.error(`[Dynamic Hearts] Failed to increase hearts for ${player.name}: ${result.error?.message}`);
                return;
            }
            const heartsAfterRespawn = result.data?.hearts ?? heartsBeforeDeath;
            const eliminated = result.data?.eliminated ?? false;
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} death handled - Before: ${heartsBeforeDeath}, After: ${heartsAfterRespawn}, Eliminated: ${eliminated}`);
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error handling player death: ${error}`);
        }
    }
    /**
     * Get event subscription count (for debugging)
     */
    getSubscriptionCount() {
        return this.eventSubscriptions.length;
    }
}
