/**
 * Dynamic Hearts System - Health Monitor
 *
 * Continuously monitors player health and enforces heart limits to prevent
 * players from exceeding their designated maximum health through healing,
 * commands, or other means.
 *
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */
import { world, EntityComponentTypes, GameMode } from '@minecraft/server';
import { HealthEnforcementAction } from './types.js';
import { SYSTEM_CONSTANTS, MESSAGES, PERFORMANCE } from './constants.js';
/**
 * Monitors and enforces player health limits
 */
export class HealthMonitor {
    playerManager;
    debugMode;
    lastMonitorTick = 0;
    performanceMetrics;
    constructor(playerManager, debugMode = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
        this.performanceMetrics = this.initializePerformanceMetrics();
    }
    /**
     * Initialize performance metrics
     */
    initializePerformanceMetrics() {
        return {
            averageTickTime: 0,
            maxTickTime: 0,
            slowOperations: 0,
            memoryUsage: {
                trackedPlayers: 0,
                cachedOperations: 0
            },
            lastCheck: Date.now()
        };
    }
    /**
     * Monitor all players' health and enforce limits
     */
    async monitorAllPlayers() {
        const startTime = Date.now();
        try {
            const players = world.getAllPlayers();
            const activePlayers = players.filter(player => {
                try {
                    return player.id !== undefined && player.getGameMode() !== GameMode.spectator;
                }
                catch {
                    return false;
                }
            });
            if (activePlayers.length === 0) {
                return;
            }
            // Process players in batches to avoid performance issues
            const batchSize = Math.min(PERFORMANCE.MAX_PLAYERS_PER_TICK, activePlayers.length);
            const playersToProcess = activePlayers.slice(0, batchSize);
            for (const player of playersToProcess) {
                try {
                    await this.monitorPlayer(player);
                }
                catch (error) {
                    console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
                }
            }
            // Update performance metrics
            this.updatePerformanceMetrics(startTime, playersToProcess.length);
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error in health monitoring: ${error}`);
        }
    }
    /**
     * Monitor a specific player's health
     */
    async monitorPlayer(player) {
        try {
            // Get player data
            const playerData = await this.playerManager.getPlayerData(player);
            if (!playerData || !playerData.initialized || playerData.eliminated) {
                return null;
            }
            // Get current health
            const currentHealth = this.playerManager.getPlayerHealth(player);
            if (currentHealth === null) {
                return null;
            }
            // Calculate expected maximum health
            const expectedMaxHealth = playerData.hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;
            // Create monitoring data
            const monitorData = {
                player,
                currentHealth,
                expectedMaxHealth,
                withinLimits: currentHealth <= expectedMaxHealth,
                recommendedAction: this.determineAction(currentHealth, expectedMaxHealth)
            };
            // Enforce health limits if necessary
            if (!monitorData.withinLimits) {
                return await this.enforceHealthLimits(monitorData);
            }
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: true
            };
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: 0,
                newHealth: 0,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Enforce health limits for a player
     */
    async enforceHealthLimits(monitorData) {
        const { player, currentHealth, expectedMaxHealth, recommendedAction } = monitorData;
        try {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (!healthComponent) {
                throw new Error('Health component not found');
            }
            let newHealth = currentHealth;
            let action = HealthEnforcementAction.NONE;
            switch (recommendedAction) {
                case HealthEnforcementAction.REDUCED:
                    // Reduce health to maximum allowed
                    const damageToApply = currentHealth - expectedMaxHealth;
                    if (damageToApply > 0) {
                        player.applyDamage(damageToApply);
                        newHealth = expectedMaxHealth;
                        action = HealthEnforcementAction.REDUCED;
                    }
                    break;
                case HealthEnforcementAction.SET_TO_MAX:
                    // Set health to maximum allowed
                    healthComponent.resetToMaxValue();
                    if (healthComponent.currentValue > expectedMaxHealth) {
                        const damageToApply = healthComponent.currentValue - expectedMaxHealth;
                        player.applyDamage(damageToApply);
                    }
                    newHealth = expectedMaxHealth;
                    action = HealthEnforcementAction.SET_TO_MAX;
                    break;
                default:
                    // No action needed
                    break;
            }
            // Update player's last known health
            const playerData = await this.playerManager.getPlayerData(player);
            if (playerData) {
                playerData.lastHealth = newHealth;
                await this.playerManager.savePlayerData(player, playerData);
            }
            if (this.debugMode && action !== HealthEnforcementAction.NONE) {
                const message = MESSAGES.DEBUG.HEALTH_ENFORCED
                    .replace('{player}', player.name)
                    .replace('{action}', action);
                console.warn(message);
            }
            return {
                action,
                previousHealth: currentHealth,
                newHealth,
                success: true
            };
        }
        catch (error) {
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Determine what action should be taken based on current and expected health
     */
    determineAction(currentHealth, expectedMaxHealth) {
        if (currentHealth <= expectedMaxHealth) {
            return HealthEnforcementAction.NONE;
        }
        if (currentHealth > expectedMaxHealth) {
            return HealthEnforcementAction.REDUCED;
        }
        return HealthEnforcementAction.NONE;
    }
    /**
     * Check if monitoring should run this tick
     */
    shouldMonitor(currentTick) {
        return (currentTick - this.lastMonitorTick) >= SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL;
    }
    /**
     * Update the last monitor tick
     */
    updateLastMonitorTick(currentTick) {
        this.lastMonitorTick = currentTick;
    }
    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(startTime, playersProcessed) {
        const executionTime = Date.now() - startTime;
        // Update average tick time
        this.performanceMetrics.averageTickTime =
            (this.performanceMetrics.averageTickTime + executionTime) / 2;
        // Update max tick time
        if (executionTime > this.performanceMetrics.maxTickTime) {
            this.performanceMetrics.maxTickTime = executionTime;
        }
        // Check for slow operations
        if (executionTime > PERFORMANCE.SLOW_OPERATION_THRESHOLD) {
            this.performanceMetrics.slowOperations++;
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Slow health monitoring operation: ${executionTime}ms for ${playersProcessed} players`);
            }
        }
        // Update memory usage
        this.performanceMetrics.memoryUsage.trackedPlayers = playersProcessed;
        this.performanceMetrics.lastCheck = Date.now();
    }
    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    /**
     * Reset performance metrics
     */
    resetPerformanceMetrics() {
        this.performanceMetrics = this.initializePerformanceMetrics();
    }
    /**
     * Get monitoring statistics
     */
    getMonitoringStats() {
        return {
            lastTick: this.lastMonitorTick,
            interval: SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL
        };
    }
}
