/**
 * Dynamic Hearts System - Health Monitor
 *
 * Continuously monitors player health and enforces heart limits to prevent
 * players from exceeding their designated maximum health through healing,
 * commands, or other means.
 *
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */
import { world, EntityComponentTypes, GameMode } from '@minecraft/server';
import { HealthEnforcementAction } from './types.js';
import { SYSTEM_CONSTANTS, MESSAGES, PERFORMANCE } from './constants.js';
/**
 * Monitors and enforces player health limits
 */
export class HealthMonitor {
    playerManager;
    debugMode;
    performanceMetrics;
    healthChangeEventCount = 0;
    constructor(playerManager, debugMode = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
        this.performanceMetrics = this.initializePerformanceMetrics();
    }
    /**
     * Initialize event-driven health monitoring
     */
    initializeEventMonitoring() {
        try {
            // Subscribe to entity health changed events
            world.afterEvents.entityHealthChanged.subscribe(async (event) => {
                try {
                    await this.handleHealthChangeEvent(event);
                }
                catch (error) {
                    console.error(`[Dynamic Hearts] Error handling health change event: ${error}`);
                }
            });
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event-driven health monitoring initialized');
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to initialize event monitoring: ${error}`);
            throw error;
        }
    }
    /**
     * Handle entity health changed event
     */
    async handleHealthChangeEvent(event) {
        const startTime = Date.now();
        try {
            const entity = event.entity;
            // Check if the entity is a player
            if (entity.typeId !== 'minecraft:player') {
                return;
            }
            const player = entity;
            // Skip if player is in spectator mode
            if (player.getGameMode() === GameMode.spectator) {
                return;
            }
            // Increment event counter for metrics
            this.healthChangeEventCount++;
            // Monitor and enforce health limits for this player
            const result = await this.monitorPlayer(player);
            if (result && !result.success && this.debugMode) {
                console.warn(`[Dynamic Hearts] Health enforcement failed for ${player.name}: ${result.error}`);
            }
            // Update performance metrics
            this.updatePerformanceMetrics(startTime, 1);
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error in health change event handler: ${error}`);
        }
    }
    /**
     * Initialize performance metrics
     */
    initializePerformanceMetrics() {
        return {
            averageTickTime: 0,
            maxTickTime: 0,
            slowOperations: 0,
            memoryUsage: {
                trackedPlayers: 0,
                cachedOperations: 0
            },
            lastCheck: Date.now()
        };
    }
    /**
     * Monitor a specific player's health
     */
    async monitorPlayer(player) {
        try {
            // Get player data
            const playerData = await this.playerManager.getPlayerData(player);
            if (!playerData || !playerData.initialized || playerData.eliminated) {
                return null;
            }
            // Get current health
            const currentHealth = this.playerManager.getPlayerHealth(player);
            if (currentHealth === null) {
                return null;
            }
            // Calculate expected maximum health
            const expectedMaxHealth = playerData.hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;
            // Create monitoring data
            const monitorData = {
                player,
                currentHealth,
                expectedMaxHealth,
                withinLimits: currentHealth <= expectedMaxHealth,
                recommendedAction: currentHealth > expectedMaxHealth ?
                    HealthEnforcementAction.REDUCED : HealthEnforcementAction.NONE
            };
            // Enforce health limits if necessary
            if (!monitorData.withinLimits) {
                return await this.enforceHealthLimits(monitorData);
            }
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: true
            };
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: 0,
                newHealth: 0,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Enforce health limits for a player
     */
    async enforceHealthLimits(monitorData) {
        const { player, currentHealth, expectedMaxHealth } = monitorData;
        try {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (!healthComponent) {
                throw new Error('Health component not found');
            }
            let newHealth = currentHealth;
            let action = HealthEnforcementAction.NONE;
            // Always set health directly to the expected maximum if it exceeds the limit
            if (currentHealth > expectedMaxHealth) {
                healthComponent.setCurrentValue(expectedMaxHealth);
                newHealth = expectedMaxHealth;
                action = HealthEnforcementAction.REDUCED;
                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Health enforced for ${player.name}: ${currentHealth} → ${expectedMaxHealth}`);
                }
            }
            // Update player's last known health
            const playerData = await this.playerManager.getPlayerData(player);
            if (playerData) {
                playerData.lastHealth = newHealth;
                await this.playerManager.savePlayerData(player, playerData);
            }
            if (this.debugMode && action !== HealthEnforcementAction.NONE) {
                const message = MESSAGES.DEBUG.HEALTH_ENFORCED
                    .replace('{player}', player.name)
                    .replace('{action}', action);
                console.warn(message);
            }
            return {
                action,
                previousHealth: currentHealth,
                newHealth,
                success: true
            };
        }
        catch (error) {
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Get interval configuration
     */
    getIntervalConfig() {
        return {
            interval: SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL
        };
    }
    /**
     * Update performance metrics for event-driven monitoring
     */
    updatePerformanceMetrics(startTime, eventsProcessed) {
        const executionTime = Date.now() - startTime;
        // Update average event processing time
        this.performanceMetrics.averageTickTime =
            (this.performanceMetrics.averageTickTime + executionTime) / 2;
        // Update max event processing time
        if (executionTime > this.performanceMetrics.maxTickTime) {
            this.performanceMetrics.maxTickTime = executionTime;
        }
        // Check for slow operations
        if (executionTime > PERFORMANCE.SLOW_OPERATION_THRESHOLD) {
            this.performanceMetrics.slowOperations++;
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Slow health event processing: ${executionTime}ms for ${eventsProcessed} events`);
            }
        }
        // Update memory usage
        this.performanceMetrics.memoryUsage.trackedPlayers = world.getAllPlayers().length;
        this.performanceMetrics.memoryUsage.cachedOperations = this.healthChangeEventCount;
        this.performanceMetrics.lastCheck = Date.now();
    }
    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    /**
     * Reset performance metrics
     */
    resetPerformanceMetrics() {
        this.performanceMetrics = this.initializePerformanceMetrics();
    }
    /**
     * Get monitoring statistics
     */
    getMonitoringStats() {
        return {
            eventsProcessed: this.healthChangeEventCount,
            lastCheck: this.performanceMetrics.lastCheck
        };
    }
}
