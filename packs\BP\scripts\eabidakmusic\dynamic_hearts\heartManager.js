/**
 * Dynamic Hearts System - Heart Manager
 *
 * Main system class that coordinates all components of the dynamic hearts system.
 * Manages initialization, tick processing, and overall system state.
 *
 * <AUTHOR> Ra<PERSON>13
 * @version 1.0.0
 */
import { world, system } from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import { EventHandlers } from './eventHandlers.js';
import { HealthMonitor } from './healthMonitor.js';
import { SYSTEM_CONSTANTS, PROPERTY_KEYS, ERROR_TYPES } from './constants.js';
/**
 * Main system class for the Dynamic Hearts system
 */
export class DynamicHeartsSystem {
    playerManager;
    eventHandlers;
    healthMonitor;
    debugMode = false;
    initialized = false;
    systemStartTime = 0;
    errorCount = 0;
    errors = [];
    // Interval ID for maintenance cleanup
    maintenanceInterval = null;
    constructor(debugMode = false) {
        this.debugMode = debugMode;
        this.playerManager = new PlayerManager(debugMode);
        this.eventHandlers = new EventHandlers(this.playerManager, debugMode);
        this.healthMonitor = new HealthMonitor(this.playerManager, debugMode);
    }
    /**
     * Initialize the dynamic hearts system
     */
    async initialize() {
        try {
            if (this.initialized) {
                console.warn('[Dynamic Hearts] System already initialized');
                return;
            }
            console.warn('[Dynamic Hearts] Initializing Dynamic Hearts System...');
            this.systemStartTime = Date.now();
            // Initialize dynamic properties
            await this.initializeDynamicProperties();
            // Initialize event handlers
            this.eventHandlers.initialize();
            // Initialize event-driven health monitoring
            this.healthMonitor.initializeEventMonitoring();
            // Initialize existing players
            await this.initializeExistingPlayers();
            // Start maintenance interval
            this.startMaintenanceInterval();
            // Mark system as initialized
            this.initialized = true;
            world.setDynamicProperty(PROPERTY_KEYS.SYSTEM_INITIALIZED, true);
            world.setDynamicProperty(PROPERTY_KEYS.SYSTEM_VERSION, SYSTEM_CONSTANTS.VERSION);
            console.warn('[Dynamic Hearts] System initialization completed successfully');
        }
        catch (error) {
            const systemError = {
                type: ERROR_TYPES.INITIALIZATION_ERROR,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                stack: error instanceof Error ? error.stack : undefined
            };
            this.handleError(systemError);
            throw error;
        }
    }
    /**
     * Start maintenance interval
     */
    startMaintenanceInterval() {
        try {
            // Start maintenance interval for system cleanup tasks
            this.maintenanceInterval = system.runInterval(async () => {
                try {
                    if (this.initialized) {
                        await this.runSystemMaintenance();
                    }
                }
                catch (error) {
                    const systemError = {
                        type: ERROR_TYPES.MONITORING_ERROR,
                        message: error instanceof Error ? error.message : String(error),
                        timestamp: Date.now()
                    };
                    this.handleError(systemError);
                }
            }, SYSTEM_CONSTANTS.MAINTENANCE_INTERVAL);
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Maintenance interval started');
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to start maintenance interval: ${error}`);
            throw error;
        }
    }
    /**
     * Main system tick - called every game tick (now simplified)
     */
    async tick() {
        // This method is now simplified since we use intervals
        // It can be used for any per-tick operations if needed in the future
        if (!this.initialized) {
            return;
        }
    }
    /**
     * Initialize dynamic properties for the world
     */
    async initializeDynamicProperties() {
        try {
            // Check if system was previously initialized
            const wasInitialized = world.getDynamicProperty(PROPERTY_KEYS.SYSTEM_INITIALIZED) ?? false;
            const systemVersion = world.getDynamicProperty(PROPERTY_KEYS.SYSTEM_VERSION) ?? '0.0.0';
            if (wasInitialized && systemVersion === SYSTEM_CONSTANTS.VERSION) {
                console.warn('[Dynamic Hearts] System properties already initialized');
                return;
            }
            // Initialize or migrate system properties
            if (wasInitialized && systemVersion !== SYSTEM_CONSTANTS.VERSION) {
                console.warn(`[Dynamic Hearts] Migrating from version ${systemVersion} to ${SYSTEM_CONSTANTS.VERSION}`);
                // Add migration logic here if needed in the future
            }
            console.warn('[Dynamic Hearts] Initializing dynamic properties...');
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to initialize dynamic properties: ${error}`);
            throw error;
        }
    }
    /**
     * Initialize all existing players in the world
     */
    async initializeExistingPlayers() {
        try {
            const players = world.getAllPlayers();
            if (players.length === 0) {
                console.warn('[Dynamic Hearts] No players found to initialize');
                return;
            }
            console.warn(`[Dynamic Hearts] Initializing ${players.length} existing players...`);
            for (const player of players) {
                try {
                    await this.playerManager.initializePlayer(player, false);
                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Initialized player: ${player.name}`);
                    }
                }
                catch (error) {
                    console.error(`[Dynamic Hearts] Failed to initialize player ${player.name}: ${error}`);
                }
            }
            console.warn('[Dynamic Hearts] Existing player initialization completed');
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error initializing existing players: ${error}`);
            throw error;
        }
    }
    /**
     * Stop maintenance interval
     */
    stopMaintenanceInterval() {
        try {
            if (this.maintenanceInterval !== null) {
                system.clearRun(this.maintenanceInterval);
                this.maintenanceInterval = null;
            }
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Maintenance interval stopped');
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error stopping maintenance interval: ${error}`);
        }
    }
    /**
     * Run system maintenance tasks
     */
    async runSystemMaintenance() {
        try {
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Running system maintenance...');
            }
            // Clean up invalid player data
            await this.cleanupInvalidPlayers();
            // Log system statistics
            if (this.debugMode) {
                const stats = this.getSystemStats();
                console.warn(`[Dynamic Hearts] System Stats - Players: ${stats.activePlayers}, Errors: ${stats.errorCount}, Uptime: ${Math.floor(stats.uptime / 1200)}min`);
            }
            // Reset performance metrics periodically
            const performanceMetrics = this.healthMonitor.getPerformanceMetrics();
            if (performanceMetrics.slowOperations > 10) {
                console.warn(`[Dynamic Hearts] Performance warning: ${performanceMetrics.slowOperations} slow operations detected`);
                this.healthMonitor.resetPerformanceMetrics();
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error during system maintenance: ${error}`);
        }
    }
    /**
     * Clean up data for players who are no longer valid
     */
    async cleanupInvalidPlayers() {
        try {
            const cachedPlayers = this.playerManager.getAllCachedPlayers();
            const validPlayerIds = new Set(world.getAllPlayers().map(p => p.id));
            for (const playerData of cachedPlayers) {
                if (!validPlayerIds.has(playerData.playerId)) {
                    this.playerManager.clearPlayerCache(playerData.playerId);
                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Cleaned up data for invalid player: ${playerData.playerId}`);
                    }
                }
            }
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error during player cleanup: ${error}`);
        }
    }
    /**
     * Handle system errors
     */
    handleError(error) {
        this.errorCount++;
        this.errors.push(error);
        // Keep only the last 100 errors to prevent memory issues
        if (this.errors.length > 100) {
            this.errors.splice(0, this.errors.length - 100);
        }
        console.error(`[Dynamic Hearts] System Error [${error.type}]: ${error.message}`);
        if (this.debugMode && error.stack) {
            console.error(`[Dynamic Hearts] Stack trace: ${error.stack}`);
        }
    }
    /**
     * Get system statistics
     */
    getSystemStats() {
        const players = world.getAllPlayers();
        const cachedPlayers = this.playerManager.getAllCachedPlayers();
        const activePlayers = players.filter(p => {
            try {
                return p.id !== undefined && p.name !== undefined;
            }
            catch {
                return false;
            }
        }).length;
        const eliminatedPlayers = cachedPlayers.filter(p => p.eliminated).length;
        const totalHearts = cachedPlayers.reduce((sum, p) => sum + p.hearts, 0);
        const averageHearts = cachedPlayers.length > 0 ? totalHearts / cachedPlayers.length : 0;
        return {
            totalPlayers: cachedPlayers.length,
            activePlayers,
            eliminatedPlayers,
            averageHearts: Math.round(averageHearts * 100) / 100,
            uptime: Date.now() - this.systemStartTime,
            errorCount: this.errorCount,
            lastUpdate: Date.now()
        };
    }
    /**
     * Get recent errors
     */
    getRecentErrors(count = 10) {
        return this.errors.slice(-count);
    }
    /**
     * Check if system is initialized
     */
    isInitialized() {
        return this.initialized;
    }
    /**
     * Shutdown the system
     */
    async shutdown() {
        try {
            console.warn('[Dynamic Hearts] Shutting down system...');
            // Stop maintenance interval
            this.stopMaintenanceInterval();
            // Cleanup event handlers
            this.eventHandlers.cleanup();
            // Clear all cached data
            this.playerManager.clearAllCache();
            // Mark as not initialized
            this.initialized = false;
            console.warn('[Dynamic Hearts] System shutdown completed');
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Error during shutdown: ${error}`);
        }
    }
}
