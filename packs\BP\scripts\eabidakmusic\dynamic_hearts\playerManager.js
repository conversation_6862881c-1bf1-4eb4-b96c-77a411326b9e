/**
 * Dynamic Hearts System - Player Manager
 *
 * Handles all player-specific operations including heart management,
 * dynamic property storage, and player state tracking.
 *
 * <AUTHOR> Raboy13
 * @version 1.0.0
 */
import { GameMode, EntityComponentTypes } from '@minecraft/server';
import { SYSTEM_CONSTANTS, PROPERTY_KEYS, MESSAGES, ERROR_TYPES } from './constants.js';
/**
 * Manages player-specific operations for the dynamic hearts system
 */
export class PlayerManager {
    playerCache = new Map();
    debugMode = false;
    constructor(debugMode = false) {
        this.debugMode = debugMode;
    }
    /**
     * Initialize a player in the dynamic hearts system
     */
    async initializePlayer(player, isFirstJoin = false) {
        try {
            const playerId = player.id;
            // Check if player is already initialized
            const existingData = await this.getPlayerData(player);
            if (existingData && existingData.initialized && !isFirstJoin) {
                return {
                    success: true,
                    data: existingData,
                    context: { reason: 'already_initialized' }
                };
            }
            // Determine initial heart count
            const initialHearts = isFirstJoin ? SYSTEM_CONSTANTS.MIN_HEARTS :
                (existingData?.hearts ?? SYSTEM_CONSTANTS.MIN_HEARTS);
            // Create player data
            const playerData = {
                playerId,
                hearts: initialHearts,
                initialized: true,
                lastHealth: initialHearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART,
                eliminated: false,
                lastUpdated: Date.now()
            };
            // Save to dynamic properties
            const saveResult = await this.savePlayerData(player, playerData);
            if (!saveResult) {
                throw new Error('Failed to save player data to dynamic properties');
            }
            // Update cache
            this.playerCache.set(playerId, playerData);
            // Set player health
            await this.setPlayerHealth(player, initialHearts);
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} initialized with ${initialHearts} hearts`);
            }
            return {
                success: true,
                data: playerData,
                context: {
                    reason: 'initialized',
                    isFirstJoin,
                    initialHearts
                }
            };
        }
        catch (error) {
            const systemError = {
                type: ERROR_TYPES.INITIALIZATION_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now(),
                stack: error instanceof Error ? error.stack : undefined
            };
            return {
                success: false,
                error: systemError
            };
        }
    }
    /**
     * Get player heart data
     */
    async getPlayerData(player) {
        try {
            const playerId = player.id;
            // Check cache first
            const cachedData = this.playerCache.get(playerId);
            if (cachedData) {
                return cachedData;
            }
            // Load from dynamic properties
            const hearts = player.getDynamicProperty(PROPERTY_KEYS.PLAYER_HEARTS) ?? SYSTEM_CONSTANTS.MIN_HEARTS;
            const initialized = player.getDynamicProperty(PROPERTY_KEYS.PLAYER_INITIALIZED) ?? false;
            const lastHealth = player.getDynamicProperty(PROPERTY_KEYS.LAST_HEALTH) ?? SYSTEM_CONSTANTS.MIN_HEALTH;
            const eliminated = player.getDynamicProperty(PROPERTY_KEYS.ELIMINATED) ?? false;
            const playerData = {
                playerId,
                hearts,
                initialized,
                lastHealth,
                eliminated,
                lastUpdated: Date.now()
            };
            // Update cache
            this.playerCache.set(playerId, playerData);
            return playerData;
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to get player data for ${player.name}: ${error}`);
            return null;
        }
    }
    /**
     * Save player heart data
     */
    async savePlayerData(player, data) {
        try {
            // Save to dynamic properties
            player.setDynamicProperty(PROPERTY_KEYS.PLAYER_HEARTS, data.hearts);
            player.setDynamicProperty(PROPERTY_KEYS.PLAYER_INITIALIZED, data.initialized);
            player.setDynamicProperty(PROPERTY_KEYS.LAST_HEALTH, data.lastHealth);
            player.setDynamicProperty(PROPERTY_KEYS.ELIMINATED, data.eliminated);
            // Update cache
            this.playerCache.set(data.playerId, { ...data, lastUpdated: Date.now() });
            return true;
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to save player data for ${player.name}: ${error}`);
            return false;
        }
    }
    /**
     * Increase player hearts (called on death)
     */
    async increasePlayerHearts(player) {
        try {
            const playerData = await this.getPlayerData(player);
            if (!playerData) {
                throw new Error('Player data not found');
            }
            const newHearts = Math.min(playerData.hearts + 1, SYSTEM_CONSTANTS.MAX_HEARTS);
            const wasAtMax = playerData.hearts >= SYSTEM_CONSTANTS.MAX_HEARTS;
            // Check if player should be eliminated
            if (wasAtMax) {
                return await this.eliminatePlayer(player);
            }
            // Update player data
            const updatedData = {
                ...playerData,
                hearts: newHearts,
                lastHealth: newHearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART,
                lastUpdated: Date.now()
            };
            // Save data
            const saveResult = await this.savePlayerData(player, updatedData);
            if (!saveResult) {
                throw new Error('Failed to save updated player data');
            }
            // Send message to player
            const message = newHearts >= SYSTEM_CONSTANTS.MAX_HEARTS ?
                MESSAGES.HEARTS.MAX_REACHED.replace('{hearts}', newHearts.toString()) :
                MESSAGES.HEARTS.GAINED.replace('{hearts}', newHearts.toString());
            player.sendMessage(message);
            return {
                success: true,
                data: updatedData,
                context: {
                    previousHearts: playerData.hearts,
                    newHearts,
                    wasAtMax
                }
            };
        }
        catch (error) {
            const systemError = {
                type: ERROR_TYPES.DYNAMIC_PROPERTY_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now()
            };
            return {
                success: false,
                error: systemError
            };
        }
    }
    /**
     * Eliminate player (switch to spectator mode)
     */
    async eliminatePlayer(player) {
        try {
            const playerData = await this.getPlayerData(player);
            if (!playerData) {
                throw new Error('Player data not found');
            }
            // Update player data
            const updatedData = {
                ...playerData,
                eliminated: true,
                lastUpdated: Date.now()
            };
            // Save data
            await this.savePlayerData(player, updatedData);
            // Switch to spectator mode
            player.setGameMode(GameMode.spectator);
            // Send elimination message
            player.sendMessage(MESSAGES.HEARTS.ELIMINATED);
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} eliminated and switched to spectator mode`);
            }
            return {
                success: true,
                data: updatedData,
                context: { reason: 'eliminated' }
            };
        }
        catch (error) {
            const systemError = {
                type: ERROR_TYPES.GAMEMODE_CHANGE_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now()
            };
            return {
                success: false,
                error: systemError
            };
        }
    }
    /**
     * Set player health to match their heart count
     */
    async setPlayerHealth(player, hearts) {
        try {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (!healthComponent) {
                throw new Error('Health component not found');
            }
            const targetHealth = hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;
            // Ensure target health is within valid bounds
            const clampedHealth = Math.max(1, Math.min(targetHealth, healthComponent.effectiveMax));
            // Set current health directly
            healthComponent.setCurrentValue(clampedHealth);
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Set ${player.name} health to ${clampedHealth} (${hearts} hearts)`);
            }
            return true;
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to set health for ${player.name}: ${error}`);
            return false;
        }
    }
    /**
     * Get current player health
     */
    getPlayerHealth(player) {
        try {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            return healthComponent?.currentValue ?? null;
        }
        catch (error) {
            console.error(`[Dynamic Hearts] Failed to get health for ${player.name}: ${error}`);
            return null;
        }
    }
    /**
     * Clear player from cache
     */
    clearPlayerCache(playerId) {
        this.playerCache.delete(playerId);
    }
    /**
     * Get all cached player data
     */
    getAllCachedPlayers() {
        return Array.from(this.playerCache.values());
    }
    /**
     * Clear all cached data
     */
    clearAllCache() {
        this.playerCache.clear();
    }
}
