/**
 * Dynamic Hearts System - Test Suite
 *
 * Simple test functions to verify the dynamic hearts system is working correctly.
 * These can be called from the console or triggered by commands for testing.
 *
 * <AUTHOR> Raboy13
 * @version 1.0.0
 */
import { world } from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import { HealthMonitor } from './healthMonitor.js';
/**
 * Test suite for the Dynamic Hearts System
 */
export class DynamicHeartsTestSuite {
    playerManager;
    healthMonitor;
    constructor() {
        this.playerManager = new PlayerManager(true); // Enable debug mode for tests
        this.healthMonitor = new HealthMonitor(this.playerManager, true);
    }
    /**
     * Run all tests
     */
    async runAllTests() {
        console.warn('[Dynamic Hearts Test] Starting test suite...');
        try {
            await this.testPlayerInitialization();
            await this.testHeartProgression();
            await this.testHealthEnforcement();
            await this.testPlayerDataPersistence();
            await this.testSystemStats();
            console.warn('[Dynamic Hearts Test] All tests completed successfully!');
            world.sendMessage('§a[Dynamic Hearts] All tests passed!');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(`[Dynamic Hearts Test] Test suite failed: ${errorMessage}`);
            world.sendMessage(`§c[Dynamic Hearts] Tests failed: ${errorMessage}`);
        }
    }
    /**
     * Test player initialization
     */
    async testPlayerInitialization() {
        console.warn('[Dynamic Hearts Test] Testing player initialization...');
        const players = world.getAllPlayers();
        if (players.length === 0) {
            throw new Error('No players found for testing');
        }
        const testPlayer = players[0];
        // Test first-time initialization
        const result = await this.playerManager.initializePlayer(testPlayer, true);
        if (!result.success) {
            throw new Error(`Player initialization failed: ${result.error?.message}`);
        }
        if (!result.data || result.data.hearts !== 1) {
            throw new Error(`Expected 1 heart, got ${result.data?.hearts}`);
        }
        console.warn('[Dynamic Hearts Test] ✓ Player initialization test passed');
    }
    /**
     * Test heart progression system
     */
    async testHeartProgression() {
        console.warn('[Dynamic Hearts Test] Testing heart progression...');
        const players = world.getAllPlayers();
        if (players.length === 0) {
            throw new Error('No players found for testing');
        }
        const testPlayer = players[0];
        // Get initial heart count
        const initialData = await this.playerManager.getPlayerData(testPlayer);
        if (!initialData) {
            throw new Error('Could not get initial player data');
        }
        const initialHearts = initialData.hearts;
        // Test heart increase
        const result = await this.playerManager.increasePlayerHearts(testPlayer);
        if (!result.success) {
            throw new Error(`Heart increase failed: ${result.error?.message}`);
        }
        if (!result.data || result.data.hearts !== initialHearts + 1) {
            throw new Error(`Expected ${initialHearts + 1} hearts, got ${result.data?.hearts}`);
        }
        console.warn('[Dynamic Hearts Test] ✓ Heart progression test passed');
    }
    /**
     * Test health enforcement
     */
    async testHealthEnforcement() {
        console.warn('[Dynamic Hearts Test] Testing health enforcement...');
        const players = world.getAllPlayers();
        if (players.length === 0) {
            throw new Error('No players found for testing');
        }
        const testPlayer = players[0];
        // Monitor player health
        const result = await this.healthMonitor.monitorPlayer(testPlayer);
        if (result === null) {
            throw new Error('Health monitoring returned null');
        }
        if (!result.success) {
            throw new Error(`Health monitoring failed: ${result.error}`);
        }
        console.warn('[Dynamic Hearts Test] ✓ Health enforcement test passed');
    }
    /**
     * Test player data persistence
     */
    async testPlayerDataPersistence() {
        console.warn('[Dynamic Hearts Test] Testing data persistence...');
        const players = world.getAllPlayers();
        if (players.length === 0) {
            throw new Error('No players found for testing');
        }
        const testPlayer = players[0];
        // Get player data
        const playerData = await this.playerManager.getPlayerData(testPlayer);
        if (!playerData) {
            throw new Error('Could not get player data');
        }
        // Modify and save data
        const modifiedData = {
            ...playerData,
            hearts: Math.min(playerData.hearts + 1, 20),
            lastUpdated: Date.now()
        };
        const saveResult = await this.playerManager.savePlayerData(testPlayer, modifiedData);
        if (!saveResult) {
            throw new Error('Failed to save player data');
        }
        // Verify data was saved
        const retrievedData = await this.playerManager.getPlayerData(testPlayer);
        if (!retrievedData || retrievedData.hearts !== modifiedData.hearts) {
            throw new Error('Data persistence verification failed');
        }
        console.warn('[Dynamic Hearts Test] ✓ Data persistence test passed');
    }
    /**
     * Test system statistics
     */
    async testSystemStats() {
        console.warn('[Dynamic Hearts Test] Testing system statistics...');
        // Get cached player data
        const cachedPlayers = this.playerManager.getAllCachedPlayers();
        if (cachedPlayers.length === 0) {
            console.warn('[Dynamic Hearts Test] No cached players found (this may be normal)');
        }
        else {
            console.warn(`[Dynamic Hearts Test] Found ${cachedPlayers.length} cached players`);
        }
        // Test performance metrics
        const performanceMetrics = this.healthMonitor.getPerformanceMetrics();
        if (typeof performanceMetrics.averageTickTime !== 'number') {
            throw new Error('Invalid performance metrics');
        }
        console.warn('[Dynamic Hearts Test] ✓ System statistics test passed');
    }
    /**
     * Test player health setting
     */
    async testPlayerHealthSetting(targetHearts) {
        console.warn(`[Dynamic Hearts Test] Testing health setting to ${targetHearts} hearts...`);
        const players = world.getAllPlayers();
        if (players.length === 0) {
            throw new Error('No players found for testing');
        }
        const testPlayer = players[0];
        const result = await this.playerManager.setPlayerHealth(testPlayer, targetHearts);
        if (!result) {
            throw new Error('Failed to set player health');
        }
        const currentHealth = this.playerManager.getPlayerHealth(testPlayer);
        const expectedHealth = targetHearts * 2; // 2 health points per heart
        if (currentHealth === null) {
            throw new Error('Could not get current health');
        }
        if (Math.abs(currentHealth - expectedHealth) > 1) { // Allow small variance
            throw new Error(`Expected ~${expectedHealth} health, got ${currentHealth}`);
        }
        console.warn(`[Dynamic Hearts Test] ✓ Health setting test passed (${currentHealth}/${expectedHealth})`);
        testPlayer.sendMessage(`§a[Test] Health set to ${targetHearts} hearts (${currentHealth} HP)`);
    }
    /**
     * Display system information
     */
    displaySystemInfo() {
        const players = world.getAllPlayers();
        const cachedPlayers = this.playerManager.getAllCachedPlayers();
        const performanceMetrics = this.healthMonitor.getPerformanceMetrics();
        console.warn('[Dynamic Hearts Test] === SYSTEM INFO ===');
        console.warn(`[Dynamic Hearts Test] Online Players: ${players.length}`);
        console.warn(`[Dynamic Hearts Test] Cached Players: ${cachedPlayers.length}`);
        console.warn(`[Dynamic Hearts Test] Average Tick Time: ${performanceMetrics.averageTickTime.toFixed(2)}ms`);
        console.warn(`[Dynamic Hearts Test] Max Tick Time: ${performanceMetrics.maxTickTime.toFixed(2)}ms`);
        console.warn(`[Dynamic Hearts Test] Slow Operations: ${performanceMetrics.slowOperations}`);
        // Send info to all players
        world.sendMessage('§e[Dynamic Hearts] System Info:');
        world.sendMessage(`§7Online: ${players.length} | Cached: ${cachedPlayers.length}`);
        world.sendMessage(`§7Avg Tick: ${performanceMetrics.averageTickTime.toFixed(2)}ms`);
    }
}
