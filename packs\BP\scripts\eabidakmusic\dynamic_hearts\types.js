/**
 * Dynamic Hearts System - TypeScript Type Definitions
 *
 * This file contains all TypeScript interfaces, types, and enums
 * used throughout the dynamic hearts system for type safety.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0.0
 */
/**
 * Health enforcement action types
 */
export var HealthEnforcementAction;
(function (HealthEnforcementAction) {
    /** No action needed - health is within limits */
    HealthEnforcementAction["NONE"] = "none";
    /** Health was reduced to maximum allowed */
    HealthEnforcementAction["REDUCED"] = "reduced";
    /** Health was set to maximum allowed (from zero/invalid) */
    HealthEnforcementAction["SET_TO_MAX"] = "set_to_max";
    /** Player was switched to spectator mode */
    HealthEnforcementAction["SPECTATOR_MODE"] = "spectator_mode";
})(HealthEnforcementAction || (HealthEnforcementAction = {}));
/**
 * System error types for better error handling
 */
export const SystemErrorType = {
    PLAYER_NOT_FOUND: 'player_not_found',
    HEALTH_COMPONENT_ERROR: 'health_component_error',
    DYNAMIC_PROPERTY_ERROR: 'dynamic_property_error',
    GAMEMODE_CHANGE_ERROR: 'gamemode_change_error',
    INITIALIZATION_ERROR: 'initialization_error',
    MONITORING_ERROR: 'monitoring_error',
};
