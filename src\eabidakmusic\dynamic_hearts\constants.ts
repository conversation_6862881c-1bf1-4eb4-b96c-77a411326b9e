/**
 * Dynamic Hearts System - Constants
 * 
 * This file contains all system constants used throughout the dynamic hearts system.
 * These values control the core behavior and can be adjusted for different gameplay experiences.
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0.0
 */

/**
 * Core system constants
 */
export const SYSTEM_CONSTANTS = {
    /** System version */
    VERSION: '1.0.0',
    
    /** System namespace for dynamic properties */
    NAMESPACE: 'dynamichearts',
    
    /** Minimum hearts a player can have */
    MIN_HEARTS: 1,
    
    /** Maximum hearts a player can have before elimination */
    MAX_HEARTS: 20,
    
    /** Health points per heart (Minecraft standard) */
    HEALTH_PER_HEART: 2,
    
    /** Minimum health value (1 heart = 2 health points) */
    MIN_HEALTH: 2,
    
    /** Maximum health value (20 hearts = 40 health points) */
    MAX_HEALTH: 40,
    
    /** How often to run health monitoring (in ticks) */
    HEALTH_CHECK_INTERVAL: 10, // Every second (10 ticks = 0.5 seconds)
    
    /** How often to run full system maintenance (in ticks) */
    MAINTENANCE_INTERVAL: 1200, // Every minute (1200 ticks = 60 seconds)
    
    /** Delay before applying health changes after spawn (in ticks) */
    SPAWN_DELAY: 40, // 2 seconds
    
    /** Maximum number of retries for critical operations */
    MAX_RETRIES: 3,
    
    /** Timeout for async operations (in milliseconds) */
    OPERATION_TIMEOUT: 5000,
} as const;

/**
 * Dynamic property keys
 */
export const PROPERTY_KEYS = {
    /** Player's current heart count */
    PLAYER_HEARTS: 'player_hearts',
    
    /** Player's initialization status */
    PLAYER_INITIALIZED: 'player_initialized',
    
    /** System initialization status */
    SYSTEM_INITIALIZED: 'system_initialized',
    
    /** Last known player health */
    LAST_HEALTH: 'last_health',
    
    /** Player elimination status */
    ELIMINATED: 'eliminated',
    
    /** System version for migration purposes */
    SYSTEM_VERSION: 'system_version',
} as const;

/**
 * Game mode constants
 */
export const GAME_MODES = {
    /** Survival mode identifier */
    SURVIVAL: 'survival',
    
    /** Creative mode identifier */
    CREATIVE: 'creative',
    
    /** Adventure mode identifier */
    ADVENTURE: 'adventure',
    
    /** Spectator mode identifier */
    SPECTATOR: 'spectator',
} as const;

/**
 * Message constants
 */
export const MESSAGES = {
    /** System initialization messages */
    INIT: {
        SUCCESS: '§a[Dynamic Hearts] System initialized successfully!',
        FAILURE: '§c[Dynamic Hearts] Failed to initialize system.',
        PLAYER_SETUP: '§e[Dynamic Hearts] Setting up player with {hearts} hearts.',
    },
    
    /** Player heart progression messages */
    HEARTS: {
        GAINED: '§a[Dynamic Hearts] You now have {hearts} hearts! (+1 heart gained)',
        MAX_REACHED: '§6[Dynamic Hearts] You have reached the maximum of {hearts} hearts!',
        ELIMINATED: '§c[Dynamic Hearts] You have been eliminated! Switching to spectator mode.',
        CURRENT: '§e[Dynamic Hearts] You currently have {hearts} hearts.',
    },
    
    /** Error messages */
    ERRORS: {
        HEALTH_COMPONENT: '§c[Dynamic Hearts] Failed to access health component.',
        PROPERTY_ACCESS: '§c[Dynamic Hearts] Failed to access player properties.',
        GAMEMODE_CHANGE: '§c[Dynamic Hearts] Failed to change game mode.',
        UNKNOWN: '§c[Dynamic Hearts] An unknown error occurred.',
    },
    
    /** Debug messages */
    DEBUG: {
        HEALTH_ENFORCED: '[Dynamic Hearts] Health enforced for player {player}: {action}',
        PLAYER_DEATH: '[Dynamic Hearts] Player {player} died with {hearts} hearts',
        PLAYER_SPAWN: '[Dynamic Hearts] Player {player} spawned with {hearts} hearts',
        SYSTEM_TICK: '[Dynamic Hearts] System tick completed - {players} players monitored',
    },
} as const;

/**
 * Health enforcement action types
 */
export const HEALTH_ACTIONS = {
    /** No action needed */
    NONE: 'none',

    /** Health was set to maximum allowed */
    REDUCED: 'reduced',

    /** Player was switched to spectator mode */
    SPECTATOR_MODE: 'spectator_mode',
} as const;

/**
 * System error types
 */
export const ERROR_TYPES = {
    /** Player not found */
    PLAYER_NOT_FOUND: 'player_not_found',
    
    /** Health component error */
    HEALTH_COMPONENT_ERROR: 'health_component_error',
    
    /** Dynamic property error */
    DYNAMIC_PROPERTY_ERROR: 'dynamic_property_error',
    
    /** Game mode change error */
    GAMEMODE_CHANGE_ERROR: 'gamemode_change_error',
    
    /** Initialization error */
    INITIALIZATION_ERROR: 'initialization_error',
    
    /** Monitoring error */
    MONITORING_ERROR: 'monitoring_error',
} as const;

/**
 * Performance monitoring constants
 */
export const PERFORMANCE = {
    /** Maximum execution time per tick (in milliseconds) */
    MAX_TICK_TIME: 16, // ~1ms per tick for 60 FPS
    
    /** Warning threshold for slow operations (in milliseconds) */
    SLOW_OPERATION_THRESHOLD: 10,
    
    /** Maximum number of players to process per tick */
    MAX_PLAYERS_PER_TICK: 10,
    
    /** Batch size for bulk operations */
    BATCH_SIZE: 5,
} as const;
