/**
 * Dynamic Hearts System - Event Handlers
 * 
 * Handles all game events related to player spawning, death, and other
 * relevant events for the dynamic hearts system.
 * 
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { 
    world, 
    system,
    Player,
    PlayerSpawnAfterEvent,
    PlayerJoinAfterEvent,
    PlayerLeaveAfterEvent,
    EntityDieAfterEvent
} from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import type {
    PlayerSpawnEventData,
    PlayerOperationResult
} from './types.js';
import { SYSTEM_CONSTANTS, MESSAGES } from './constants.js';

/**
 * Manages all event handling for the dynamic hearts system
 */
export class EventHandlers {
    private readonly playerManager: PlayerManager;
    private readonly debugMode: boolean;
    private readonly eventSubscriptions: (() => void)[] = [];

    constructor(playerManager: PlayerManager, debugMode: boolean = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
    }

    /**
     * Initialize all event handlers
     */
    public initialize(): void {
        try {
            this.setupPlayerJoinHandler();
            this.setupPlayerLeaveHandler();
            this.setupPlayerSpawnHandler();
            this.setupEntityDeathHandler();

            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event handlers initialized successfully');
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to initialize event handlers: ${error}`);
            throw error;
        }
    }

    /**
     * Clean up all event subscriptions
     */
    public cleanup(): void {
        try {
            // Unsubscribe from all events
            this.eventSubscriptions.forEach(unsubscribe => {
                try {
                    unsubscribe();
                } catch (error: unknown) {
                    console.error(`[Dynamic Hearts] Error unsubscribing from event: ${error}`);
                }
            });

            this.eventSubscriptions.length = 0;

            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event handlers cleaned up');
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error during event handler cleanup: ${error}`);
        }
    }

    /**
     * Setup player join event handler
     */
    private setupPlayerJoinHandler(): void {
        world.afterEvents.playerJoin.subscribe(async (event: PlayerJoinAfterEvent) => {
            try {
                // Get player by ID since the event only provides playerId
                const players: Player[] = world.getAllPlayers();
                const player: Player | undefined = players.find(p => p.id === event.playerId);

                if (!player) {
                    console.error(`[Dynamic Hearts] Could not find player with ID: ${event.playerId}`);
                    return;
                }

                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${player.name} joined the game`);
                }

                // Initialize player with a delay to ensure they're fully loaded
                system.runTimeout(async () => {
                    try {
                        const result: PlayerOperationResult = await this.playerManager.initializePlayer(player, true);

                        if (!result.success) {
                            console.error(`[Dynamic Hearts] Failed to initialize player ${player.name}: ${result.error?.message}`);
                            player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
                        } else if (result.data) {
                            const message: string = MESSAGES.INIT.PLAYER_SETUP
                                .replace('{hearts}', result.data.hearts.toString());
                            player.sendMessage(message);
                        }

                    } catch (error: unknown) {
                        console.error(`[Dynamic Hearts] Error in delayed player initialization: ${error}`);
                    }
                }, SYSTEM_CONSTANTS.SPAWN_DELAY);

            } catch (error: unknown) {
                console.error(`[Dynamic Hearts] Error in player join handler: ${error}`);
            }
        });

        // Store a simple cleanup function since we can't properly unsubscribe
        this.eventSubscriptions.push(() => {
            // Event subscriptions in Minecraft Bedrock cannot be easily unsubscribed
            // This is a placeholder for cleanup
        });
    }

    /**
     * Setup player leave event handler
     */
    private setupPlayerLeaveHandler(): void {
        world.afterEvents.playerLeave.subscribe((event: PlayerLeaveAfterEvent) => {
            try {
                const playerId: string = event.playerId;

                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${playerId} left the game`);
                }

                // Clear player from cache to free memory
                this.playerManager.clearPlayerCache(playerId);

            } catch (error: unknown) {
                console.error(`[Dynamic Hearts] Error in player leave handler: ${error}`);
            }
        });

        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }

    /**
     * Setup player spawn event handler
     */
    private setupPlayerSpawnHandler(): void {
        world.afterEvents.playerSpawn.subscribe(async (event: PlayerSpawnAfterEvent) => {
            try {
                const player: Player = event.player;
                const initialSpawn: boolean = event.initialSpawn;

                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Player ${player.name} spawned (initial: ${initialSpawn})`);
                }

                // Handle spawn with a delay to ensure player is fully loaded
                system.runTimeout(async () => {
                    try {
                        await this.handlePlayerSpawn(player, initialSpawn);
                    } catch (error: unknown) {
                        console.error(`[Dynamic Hearts] Error in delayed spawn handler: ${error}`);
                    }
                }, SYSTEM_CONSTANTS.SPAWN_DELAY);

            } catch (error: unknown) {
                console.error(`[Dynamic Hearts] Error in player spawn handler: ${error}`);
            }
        });

        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }

    /**
     * Setup entity death event handler (for players)
     */
    private setupEntityDeathHandler(): void {
        world.afterEvents.entityDie.subscribe(async (event: EntityDieAfterEvent) => {
            try {
                const entity = event.deadEntity;

                // Check if the dead entity is a player
                if (entity.typeId === 'minecraft:player') {
                    const player: Player = entity as Player;

                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Player ${player.name} died`);
                    }

                    await this.handlePlayerDeath(player);
                }

            } catch (error: unknown) {
                console.error(`[Dynamic Hearts] Error in entity death handler: ${error}`);
            }
        });

        this.eventSubscriptions.push(() => {
            // Placeholder for cleanup
        });
    }

    /**
     * Handle player spawn event
     */
    private async handlePlayerSpawn(player: Player, initialSpawn: boolean): Promise<void> {
        try {
            // Get or initialize player data
            let result: PlayerOperationResult;
            
            if (initialSpawn) {
                result = await this.playerManager.initializePlayer(player, true);
            } else {
                // For respawns, just ensure player is initialized
                result = await this.playerManager.initializePlayer(player, false);
                
                // Set health to match current heart count
                if (result.success && result.data) {
                    await this.playerManager.setPlayerHealth(player, result.data.hearts);
                }
            }

            if (!result.success) {
                console.error(`[Dynamic Hearts] Failed to handle player spawn for ${player.name}: ${result.error?.message}`);
                player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
                return;
            }

            // Create spawn event data
            const spawnEventData: PlayerSpawnEventData = {
                player,
                initialSpawn,
                hearts: result.data?.hearts ?? SYSTEM_CONSTANTS.MIN_HEARTS,
                timestamp: Date.now()
            };

            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} spawn handled - Hearts: ${spawnEventData.hearts}`);
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error handling player spawn: ${error}`);
            player.sendMessage(MESSAGES.ERRORS.UNKNOWN);
        }
    }

    /**
     * Handle player death event
     */
    private async handlePlayerDeath(player: Player): Promise<void> {
        try {
            // Get current player data
            const playerData = await this.playerManager.getPlayerData(player);
            if (!playerData) {
                console.error(`[Dynamic Hearts] No player data found for ${player.name} on death`);
                return;
            }

            const heartsBeforeDeath: number = playerData.hearts;
            
            // Increase hearts for next life (or eliminate if at max)
            const result: PlayerOperationResult = await this.playerManager.increasePlayerHearts(player);
            
            if (!result.success) {
                console.error(`[Dynamic Hearts] Failed to increase hearts for ${player.name}: ${result.error?.message}`);
                return;
            }

            const heartsAfterRespawn: number = result.data?.hearts ?? heartsBeforeDeath;
            const eliminated: boolean = result.data?.eliminated ?? false;

            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} death handled - Before: ${heartsBeforeDeath}, After: ${heartsAfterRespawn}, Eliminated: ${eliminated}`);
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error handling player death: ${error}`);
        }
    }

    /**
     * Get event subscription count (for debugging)
     */
    public getSubscriptionCount(): number {
        return this.eventSubscriptions.length;
    }
}
