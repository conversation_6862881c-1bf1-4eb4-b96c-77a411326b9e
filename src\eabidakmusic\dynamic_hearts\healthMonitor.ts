/**
 * Dynamic Hearts System - Health Monitor
 * 
 * Continuously monitors player health and enforces heart limits to prevent
 * players from exceeding their designated maximum health through healing,
 * commands, or other means.
 * 
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { world, Player, EntityHealthComponent, EntityComponentTypes, GameMode, EntityHealthChangedAfterEvent } from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import type { 
    HealthMonitorData, 
    HealthEnforcementResult, 
    PlayerHeartData,
    PerformanceMetrics 
} from './types.js';
import {
    HealthEnforcementAction
} from './types.js';
import {
    SYSTEM_CONSTANTS,
    MESSAGES,
    PERFORMANCE
} from './constants.js';

/**
 * Monitors and enforces player health limits
 */
export class HealthMonitor {
    private readonly playerManager: PlayerManager;
    private readonly debugMode: boolean;
    private performanceMetrics: PerformanceMetrics;
    private healthChangeEventCount: number = 0;

    constructor(playerManager: PlayerManager, debugMode: boolean = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
        this.performanceMetrics = this.initializePerformanceMetrics();
    }

    /**
     * Initialize event-driven health monitoring
     */
    public initializeEventMonitoring(): void {
        try {
            // Subscribe to entity health changed events
            world.afterEvents.entityHealthChanged.subscribe(async (event: EntityHealthChangedAfterEvent) => {
                try {
                    await this.handleHealthChangeEvent(event);
                } catch (error: unknown) {
                    console.error(`[Dynamic Hearts] Error handling health change event: ${error}`);
                }
            });

            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Event-driven health monitoring initialized');
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to initialize event monitoring: ${error}`);
            throw error;
        }
    }

    /**
     * Handle entity health changed event
     */
    private async handleHealthChangeEvent(event: EntityHealthChangedAfterEvent): Promise<void> {
        const startTime: number = Date.now();

        try {
            const entity = event.entity;

            // Check if the entity is a player
            if (entity.typeId !== 'minecraft:player') {
                return;
            }

            const player: Player = entity as Player;

            // Skip if player is in spectator mode
            if (player.getGameMode() === GameMode.spectator) {
                return;
            }

            // Increment event counter for metrics
            this.healthChangeEventCount++;

            // Monitor and enforce health limits for this player
            const result = await this.monitorPlayer(player);

            if (result && !result.success && this.debugMode) {
                console.warn(`[Dynamic Hearts] Health enforcement failed for ${player.name}: ${result.error}`);
            }

            // Update performance metrics
            this.updatePerformanceMetrics(startTime, 1);

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error in health change event handler: ${error}`);
        }
    }

    /**
     * Initialize performance metrics
     */
    private initializePerformanceMetrics(): PerformanceMetrics {
        return {
            averageTickTime: 0,
            maxTickTime: 0,
            slowOperations: 0,
            memoryUsage: {
                trackedPlayers: 0,
                cachedOperations: 0
            },
            lastCheck: Date.now()
        };
    }



    /**
     * Monitor a specific player's health
     */
    public async monitorPlayer(player: Player): Promise<HealthEnforcementResult | null> {
        try {
            // Get player data
            const playerData: PlayerHeartData | null = await this.playerManager.getPlayerData(player);
            if (!playerData || !playerData.initialized || playerData.eliminated) {
                return null;
            }

            // Get current health
            const currentHealth: number | null = this.playerManager.getPlayerHealth(player);
            if (currentHealth === null) {
                return null;
            }

            // Calculate expected maximum health
            const expectedMaxHealth: number = playerData.hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;

            // Create monitoring data
            const monitorData: HealthMonitorData = {
                player,
                currentHealth,
                expectedMaxHealth,
                withinLimits: currentHealth <= expectedMaxHealth,
                recommendedAction: currentHealth > expectedMaxHealth ?
                    HealthEnforcementAction.REDUCED : HealthEnforcementAction.NONE
            };

            // Enforce health limits if necessary
            if (!monitorData.withinLimits) {
                return await this.enforceHealthLimits(monitorData);
            }

            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: true
            };

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: 0,
                newHealth: 0,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Enforce health limits for a player
     */
    private async enforceHealthLimits(monitorData: HealthMonitorData): Promise<HealthEnforcementResult> {
        const { player, currentHealth, expectedMaxHealth } = monitorData;

        try {
            const healthComponent: EntityHealthComponent | undefined = 
                player.getComponent(EntityComponentTypes.Health) as EntityHealthComponent;

            if (!healthComponent) {
                throw new Error('Health component not found');
            }

            let newHealth: number = currentHealth;
            let action: HealthEnforcementAction = HealthEnforcementAction.NONE;

            // Always set health directly to the expected maximum if it exceeds the limit
            if (currentHealth > expectedMaxHealth) {
                healthComponent.setCurrentValue(expectedMaxHealth);
                newHealth = expectedMaxHealth;
                action = HealthEnforcementAction.REDUCED;

                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Health enforced for ${player.name}: ${currentHealth} → ${expectedMaxHealth}`);
                }
            }

            // Update player's last known health
            const playerData: PlayerHeartData | null = await this.playerManager.getPlayerData(player);
            if (playerData) {
                playerData.lastHealth = newHealth;
                await this.playerManager.savePlayerData(player, playerData);
            }

            if (this.debugMode && action !== HealthEnforcementAction.NONE) {
                const message: string = MESSAGES.DEBUG.HEALTH_ENFORCED
                    .replace('{player}', player.name)
                    .replace('{action}', action);
                console.warn(message);
            }

            return {
                action,
                previousHealth: currentHealth,
                newHealth,
                success: true
            };

        } catch (error: unknown) {
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }



    /**
     * Get interval configuration
     */
    public getIntervalConfig(): { interval: number } {
        return {
            interval: SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL
        };
    }

    /**
     * Update performance metrics for event-driven monitoring
     */
    private updatePerformanceMetrics(startTime: number, eventsProcessed: number): void {
        const executionTime: number = Date.now() - startTime;

        // Update average event processing time
        this.performanceMetrics.averageTickTime =
            (this.performanceMetrics.averageTickTime + executionTime) / 2;

        // Update max event processing time
        if (executionTime > this.performanceMetrics.maxTickTime) {
            this.performanceMetrics.maxTickTime = executionTime;
        }

        // Check for slow operations
        if (executionTime > PERFORMANCE.SLOW_OPERATION_THRESHOLD) {
            this.performanceMetrics.slowOperations++;

            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Slow health event processing: ${executionTime}ms for ${eventsProcessed} events`);
            }
        }

        // Update memory usage
        this.performanceMetrics.memoryUsage.trackedPlayers = world.getAllPlayers().length;
        this.performanceMetrics.memoryUsage.cachedOperations = this.healthChangeEventCount;
        this.performanceMetrics.lastCheck = Date.now();
    }

    /**
     * Get performance metrics
     */
    public getPerformanceMetrics(): PerformanceMetrics {
        return { ...this.performanceMetrics };
    }

    /**
     * Reset performance metrics
     */
    public resetPerformanceMetrics(): void {
        this.performanceMetrics = this.initializePerformanceMetrics();
    }

    /**
     * Get monitoring statistics
     */
    public getMonitoringStats(): { eventsProcessed: number; lastCheck: number } {
        return {
            eventsProcessed: this.healthChangeEventCount,
            lastCheck: this.performanceMetrics.lastCheck
        };
    }
}
