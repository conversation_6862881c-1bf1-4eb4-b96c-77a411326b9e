/**
 * Dynamic Hearts System - Health Monitor
 * 
 * Continuously monitors player health and enforces heart limits to prevent
 * players from exceeding their designated maximum health through healing,
 * commands, or other means.
 * 
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { world, Player, EntityHealthComponent, EntityComponentTypes, GameMode } from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import type { 
    HealthMonitorData, 
    HealthEnforcementResult, 
    PlayerHeartData,
    PerformanceMetrics 
} from './types.js';
import {
    HealthEnforcementAction
} from './types.js';
import {
    SYSTEM_CONSTANTS,
    MESSAGES,
    PERFORMANCE
} from './constants.js';

/**
 * Monitors and enforces player health limits
 */
export class HealthMonitor {
    private readonly playerManager: PlayerManager;
    private readonly debugMode: boolean;
    private performanceMetrics: PerformanceMetrics;

    constructor(playerManager: PlayerManager, debugMode: boolean = false) {
        this.playerManager = playerManager;
        this.debugMode = debugMode;
        this.performanceMetrics = this.initializePerformanceMetrics();
    }

    /**
     * Initialize performance metrics
     */
    private initializePerformanceMetrics(): PerformanceMetrics {
        return {
            averageTickTime: 0,
            maxTickTime: 0,
            slowOperations: 0,
            memoryUsage: {
                trackedPlayers: 0,
                cachedOperations: 0
            },
            lastCheck: Date.now()
        };
    }

    /**
     * Monitor all players' health and enforce limits
     */
    public async monitorAllPlayers(): Promise<void> {
        const startTime: number = Date.now();
        
        try {
            const players: Player[] = world.getAllPlayers();
            const activePlayers: Player[] = players.filter(player => {
                try {
                    return player.id !== undefined && player.getGameMode() !== GameMode.spectator;
                } catch {
                    return false;
                }
            });

            if (activePlayers.length === 0) {
                return;
            }

            // Process players in batches to avoid performance issues
            const batchSize: number = Math.min(PERFORMANCE.MAX_PLAYERS_PER_TICK, activePlayers.length);
            const playersToProcess: Player[] = activePlayers.slice(0, batchSize);

            for (const player of playersToProcess) {
                try {
                    await this.monitorPlayer(player);
                } catch (error: unknown) {
                    console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
                }
            }

            // Update performance metrics
            this.updatePerformanceMetrics(startTime, playersToProcess.length);

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error in health monitoring: ${error}`);
        }
    }

    /**
     * Monitor a specific player's health
     */
    public async monitorPlayer(player: Player): Promise<HealthEnforcementResult | null> {
        try {
            // Get player data
            const playerData: PlayerHeartData | null = await this.playerManager.getPlayerData(player);
            if (!playerData || !playerData.initialized || playerData.eliminated) {
                return null;
            }

            // Get current health
            const currentHealth: number | null = this.playerManager.getPlayerHealth(player);
            if (currentHealth === null) {
                return null;
            }

            // Calculate expected maximum health
            const expectedMaxHealth: number = playerData.hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;

            // Create monitoring data
            const monitorData: HealthMonitorData = {
                player,
                currentHealth,
                expectedMaxHealth,
                withinLimits: currentHealth <= expectedMaxHealth,
                recommendedAction: currentHealth > expectedMaxHealth ?
                    HealthEnforcementAction.REDUCED : HealthEnforcementAction.NONE
            };

            // Enforce health limits if necessary
            if (!monitorData.withinLimits) {
                return await this.enforceHealthLimits(monitorData);
            }

            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: true
            };

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error monitoring player ${player.name}: ${error}`);
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: 0,
                newHealth: 0,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Enforce health limits for a player
     */
    private async enforceHealthLimits(monitorData: HealthMonitorData): Promise<HealthEnforcementResult> {
        const { player, currentHealth, expectedMaxHealth } = monitorData;

        try {
            const healthComponent: EntityHealthComponent | undefined = 
                player.getComponent(EntityComponentTypes.Health) as EntityHealthComponent;

            if (!healthComponent) {
                throw new Error('Health component not found');
            }

            let newHealth: number = currentHealth;
            let action: HealthEnforcementAction = HealthEnforcementAction.NONE;

            // Always set health directly to the expected maximum if it exceeds the limit
            if (currentHealth > expectedMaxHealth) {
                healthComponent.setCurrentValue(expectedMaxHealth);
                newHealth = expectedMaxHealth;
                action = HealthEnforcementAction.REDUCED;

                if (this.debugMode) {
                    console.warn(`[Dynamic Hearts] Health enforced for ${player.name}: ${currentHealth} → ${expectedMaxHealth}`);
                }
            }

            // Update player's last known health
            const playerData: PlayerHeartData | null = await this.playerManager.getPlayerData(player);
            if (playerData) {
                playerData.lastHealth = newHealth;
                await this.playerManager.savePlayerData(player, playerData);
            }

            if (this.debugMode && action !== HealthEnforcementAction.NONE) {
                const message: string = MESSAGES.DEBUG.HEALTH_ENFORCED
                    .replace('{player}', player.name)
                    .replace('{action}', action);
                console.warn(message);
            }

            return {
                action,
                previousHealth: currentHealth,
                newHealth,
                success: true
            };

        } catch (error: unknown) {
            return {
                action: HealthEnforcementAction.NONE,
                previousHealth: currentHealth,
                newHealth: currentHealth,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }



    /**
     * Get interval configuration
     */
    public getIntervalConfig(): { interval: number } {
        return {
            interval: SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL
        };
    }

    /**
     * Update performance metrics
     */
    private updatePerformanceMetrics(startTime: number, playersProcessed: number): void {
        const executionTime: number = Date.now() - startTime;
        
        // Update average tick time
        this.performanceMetrics.averageTickTime = 
            (this.performanceMetrics.averageTickTime + executionTime) / 2;

        // Update max tick time
        if (executionTime > this.performanceMetrics.maxTickTime) {
            this.performanceMetrics.maxTickTime = executionTime;
        }

        // Check for slow operations
        if (executionTime > PERFORMANCE.SLOW_OPERATION_THRESHOLD) {
            this.performanceMetrics.slowOperations++;
            
            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Slow health monitoring operation: ${executionTime}ms for ${playersProcessed} players`);
            }
        }

        // Update memory usage
        this.performanceMetrics.memoryUsage.trackedPlayers = playersProcessed;
        this.performanceMetrics.lastCheck = Date.now();
    }

    /**
     * Get performance metrics
     */
    public getPerformanceMetrics(): PerformanceMetrics {
        return { ...this.performanceMetrics };
    }

    /**
     * Reset performance metrics
     */
    public resetPerformanceMetrics(): void {
        this.performanceMetrics = this.initializePerformanceMetrics();
    }

    /**
     * Get monitoring statistics
     */
    public getMonitoringStats(): { interval: number; lastCheck: number } {
        return {
            interval: SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL,
            lastCheck: this.performanceMetrics.lastCheck
        };
    }
}
