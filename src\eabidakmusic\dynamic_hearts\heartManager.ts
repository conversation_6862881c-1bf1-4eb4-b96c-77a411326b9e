/**
 * Dynamic Hearts System - Heart Manager
 * 
 * Main system class that coordinates all components of the dynamic hearts system.
 * Manages initialization, tick processing, and overall system state.
 * 
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { world, system, Player } from '@minecraft/server';
import { PlayerManager } from './playerManager.js';
import { EventHandlers } from './eventHandlers.js';
import { HealthMonitor } from './healthMonitor.js';
import type {
    SystemStats,
    SystemError,
    PlayerHeartData,
    PerformanceMetrics
} from './types.js';
import {
    SYSTEM_CONSTANTS,
    PROPERTY_KEYS,
    ERROR_TYPES
} from './constants.js';

/**
 * Main system class for the Dynamic Hearts system
 */
export class DynamicHeartsSystem {
    private readonly playerManager: PlayerManager;
    private readonly eventHandlers: EventHandlers;
    private readonly healthMonitor: HealthMonitor;
    private readonly debugMode: boolean = false;

    private initialized: boolean = false;
    private systemStartTime: number = 0;
    private errorCount: number = 0;
    private readonly errors: SystemError[] = [];

    // Interval IDs for cleanup
    private healthMonitoringInterval: number | null = null;
    private maintenanceInterval: number | null = null;

    constructor(debugMode: boolean = false) {
        this.debugMode = debugMode;
        this.playerManager = new PlayerManager(debugMode);
        this.eventHandlers = new EventHandlers(this.playerManager, debugMode);
        this.healthMonitor = new HealthMonitor(this.playerManager, debugMode);
    }

    /**
     * Initialize the dynamic hearts system
     */
    public async initialize(): Promise<void> {
        try {
            if (this.initialized) {
                console.warn('[Dynamic Hearts] System already initialized');
                return;
            }

            console.warn('[Dynamic Hearts] Initializing Dynamic Hearts System...');
            this.systemStartTime = Date.now();

            // Initialize dynamic properties
            await this.initializeDynamicProperties();

            // Initialize event handlers
            this.eventHandlers.initialize();

            // Initialize existing players
            await this.initializeExistingPlayers();

            // Start monitoring intervals
            this.startMonitoringIntervals();

            // Mark system as initialized
            this.initialized = true;
            world.setDynamicProperty(PROPERTY_KEYS.SYSTEM_INITIALIZED, true);
            world.setDynamicProperty(PROPERTY_KEYS.SYSTEM_VERSION, SYSTEM_CONSTANTS.VERSION);

            console.warn('[Dynamic Hearts] System initialization completed successfully');

        } catch (error: unknown) {
            const systemError: SystemError = {
                type: ERROR_TYPES.INITIALIZATION_ERROR,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                stack: error instanceof Error ? error.stack : undefined
            };

            this.handleError(systemError);
            throw error;
        }
    }

    /**
     * Start monitoring intervals
     */
    private startMonitoringIntervals(): void {
        try {
            // Start health monitoring interval
            this.healthMonitoringInterval = system.runInterval(async () => {
                try {
                    if (this.initialized) {
                        await this.healthMonitor.monitorAllPlayers();
                    }
                } catch (error: unknown) {
                    const systemError: SystemError = {
                        type: ERROR_TYPES.MONITORING_ERROR,
                        message: error instanceof Error ? error.message : String(error),
                        timestamp: Date.now()
                    };
                    this.handleError(systemError);
                }
            }, SYSTEM_CONSTANTS.HEALTH_CHECK_INTERVAL);

            // Start maintenance interval
            this.maintenanceInterval = system.runInterval(async () => {
                try {
                    if (this.initialized) {
                        await this.runSystemMaintenance();
                    }
                } catch (error: unknown) {
                    const systemError: SystemError = {
                        type: ERROR_TYPES.MONITORING_ERROR,
                        message: error instanceof Error ? error.message : String(error),
                        timestamp: Date.now()
                    };
                    this.handleError(systemError);
                }
            }, SYSTEM_CONSTANTS.MAINTENANCE_INTERVAL);

            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Monitoring intervals started');
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to start monitoring intervals: ${error}`);
            throw error;
        }
    }

    /**
     * Main system tick - called every game tick (now simplified)
     */
    public async tick(): Promise<void> {
        // This method is now simplified since we use intervals
        // It can be used for any per-tick operations if needed in the future
        if (!this.initialized) {
            return;
        }
    }

    /**
     * Initialize dynamic properties for the world
     */
    private async initializeDynamicProperties(): Promise<void> {
        try {
            // Check if system was previously initialized
            const wasInitialized: boolean = world.getDynamicProperty(PROPERTY_KEYS.SYSTEM_INITIALIZED) as boolean ?? false;
            const systemVersion: string = world.getDynamicProperty(PROPERTY_KEYS.SYSTEM_VERSION) as string ?? '0.0.0';

            if (wasInitialized && systemVersion === SYSTEM_CONSTANTS.VERSION) {
                console.warn('[Dynamic Hearts] System properties already initialized');
                return;
            }

            // Initialize or migrate system properties
            if (wasInitialized && systemVersion !== SYSTEM_CONSTANTS.VERSION) {
                console.warn(`[Dynamic Hearts] Migrating from version ${systemVersion} to ${SYSTEM_CONSTANTS.VERSION}`);
                // Add migration logic here if needed in the future
            }

            console.warn('[Dynamic Hearts] Initializing dynamic properties...');

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to initialize dynamic properties: ${error}`);
            throw error;
        }
    }

    /**
     * Initialize all existing players in the world
     */
    private async initializeExistingPlayers(): Promise<void> {
        try {
            const players: Player[] = world.getAllPlayers();
            
            if (players.length === 0) {
                console.warn('[Dynamic Hearts] No players found to initialize');
                return;
            }

            console.warn(`[Dynamic Hearts] Initializing ${players.length} existing players...`);

            for (const player of players) {
                try {
                    await this.playerManager.initializePlayer(player, false);
                    
                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Initialized player: ${player.name}`);
                    }

                } catch (error: unknown) {
                    console.error(`[Dynamic Hearts] Failed to initialize player ${player.name}: ${error}`);
                }
            }

            console.warn('[Dynamic Hearts] Existing player initialization completed');

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error initializing existing players: ${error}`);
            throw error;
        }
    }

    /**
     * Stop monitoring intervals
     */
    private stopMonitoringIntervals(): void {
        try {
            if (this.healthMonitoringInterval !== null) {
                system.clearRun(this.healthMonitoringInterval);
                this.healthMonitoringInterval = null;
            }

            if (this.maintenanceInterval !== null) {
                system.clearRun(this.maintenanceInterval);
                this.maintenanceInterval = null;
            }

            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Monitoring intervals stopped');
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error stopping monitoring intervals: ${error}`);
        }
    }

    /**
     * Run system maintenance tasks
     */
    private async runSystemMaintenance(): Promise<void> {
        try {
            if (this.debugMode) {
                console.warn('[Dynamic Hearts] Running system maintenance...');
            }

            // Clean up invalid player data
            await this.cleanupInvalidPlayers();

            // Log system statistics
            if (this.debugMode) {
                const stats: SystemStats = this.getSystemStats();
                console.warn(`[Dynamic Hearts] System Stats - Players: ${stats.activePlayers}, Errors: ${stats.errorCount}, Uptime: ${Math.floor(stats.uptime / 1200)}min`);
            }

            // Reset performance metrics periodically
            const performanceMetrics: PerformanceMetrics = this.healthMonitor.getPerformanceMetrics();
            if (performanceMetrics.slowOperations > 10) {
                console.warn(`[Dynamic Hearts] Performance warning: ${performanceMetrics.slowOperations} slow operations detected`);
                this.healthMonitor.resetPerformanceMetrics();
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error during system maintenance: ${error}`);
        }
    }

    /**
     * Clean up data for players who are no longer valid
     */
    private async cleanupInvalidPlayers(): Promise<void> {
        try {
            const cachedPlayers: PlayerHeartData[] = this.playerManager.getAllCachedPlayers();
            const validPlayerIds: Set<string> = new Set(world.getAllPlayers().map(p => p.id));

            for (const playerData of cachedPlayers) {
                if (!validPlayerIds.has(playerData.playerId)) {
                    this.playerManager.clearPlayerCache(playerData.playerId);
                    
                    if (this.debugMode) {
                        console.warn(`[Dynamic Hearts] Cleaned up data for invalid player: ${playerData.playerId}`);
                    }
                }
            }

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error during player cleanup: ${error}`);
        }
    }

    /**
     * Handle system errors
     */
    private handleError(error: SystemError): void {
        this.errorCount++;
        this.errors.push(error);

        // Keep only the last 100 errors to prevent memory issues
        if (this.errors.length > 100) {
            this.errors.splice(0, this.errors.length - 100);
        }

        console.error(`[Dynamic Hearts] System Error [${error.type}]: ${error.message}`);

        if (this.debugMode && error.stack) {
            console.error(`[Dynamic Hearts] Stack trace: ${error.stack}`);
        }
    }

    /**
     * Get system statistics
     */
    public getSystemStats(): SystemStats {
        const players: Player[] = world.getAllPlayers();
        const cachedPlayers: PlayerHeartData[] = this.playerManager.getAllCachedPlayers();
        
        const activePlayers: number = players.filter(p => {
            try {
                return p.id !== undefined && p.name !== undefined;
            } catch {
                return false;
            }
        }).length;
        const eliminatedPlayers: number = cachedPlayers.filter(p => p.eliminated).length;
        const totalHearts: number = cachedPlayers.reduce((sum, p) => sum + p.hearts, 0);
        const averageHearts: number = cachedPlayers.length > 0 ? totalHearts / cachedPlayers.length : 0;

        return {
            totalPlayers: cachedPlayers.length,
            activePlayers,
            eliminatedPlayers,
            averageHearts: Math.round(averageHearts * 100) / 100,
            uptime: Date.now() - this.systemStartTime,
            errorCount: this.errorCount,
            lastUpdate: Date.now()
        };
    }

    /**
     * Get recent errors
     */
    public getRecentErrors(count: number = 10): SystemError[] {
        return this.errors.slice(-count);
    }

    /**
     * Check if system is initialized
     */
    public isInitialized(): boolean {
        return this.initialized;
    }

    /**
     * Shutdown the system
     */
    public async shutdown(): Promise<void> {
        try {
            console.warn('[Dynamic Hearts] Shutting down system...');

            // Stop monitoring intervals
            this.stopMonitoringIntervals();

            // Cleanup event handlers
            this.eventHandlers.cleanup();

            // Clear all cached data
            this.playerManager.clearAllCache();

            // Mark as not initialized
            this.initialized = false;

            console.warn('[Dynamic Hearts] System shutdown completed');

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Error during shutdown: ${error}`);
        }
    }
}
