/**
 * Dynamic Hearts System - Main Entry Point
 *
 * A comprehensive Minecraft Bedrock add-on that implements a progressive heart system
 * where players start with 1 heart and gain additional hearts upon death, up to a maximum
 * of 20 hearts. Players who die at 20 hearts are switched to spectator mode.
 *
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { world, system } from '@minecraft/server';
import { SYSTEM_CONSTANTS } from './constants';
import { DynamicHeartsSystem } from './heartManager';

/**
 * Main system instance
 */
let dynamicHeartsSystem: DynamicHeartsSystem | null = null;

/**
 * Initialize the Dynamic Hearts System
 */
function initializeDynamicHeartsSystem(): void {
    try {
        console.warn(`[Dynamic Hearts] Initializing system v${SYSTEM_CONSTANTS.VERSION}...`);

        // Create the main system instance
        dynamicHeartsSystem = new DynamicHeartsSystem();

        // Initialize the system
        dynamicHeartsSystem.initialize();

        console.warn('[Dynamic Hearts] System initialized successfully!');
        world.sendMessage('§a[Dynamic Hearts] System activated! Players start with 1 heart and gain hearts upon death.');

    } catch (error: unknown) {
        const errorMessage: string = error instanceof Error ? error.message : String(error);
        console.error(`[Dynamic Hearts] Failed to initialize system: ${errorMessage}`);
        world.sendMessage('§c[Dynamic Hearts] Failed to initialize system. Check console for details.');
    }
}

/**
 * Main initialization function
 */
function initializeSystem(): void {
    try {
        // Initialize system
        if (!dynamicHeartsSystem) {
            initializeDynamicHeartsSystem();
        }

    } catch (error: unknown) {
        const errorMessage: string = error instanceof Error ? error.message : String(error);
        console.error(`[Dynamic Hearts] Error in system initialization: ${errorMessage}`);
    }
}

// Start the system
console.warn('[Dynamic Hearts] Starting Dynamic Hearts System...');
system.run(initializeSystem);