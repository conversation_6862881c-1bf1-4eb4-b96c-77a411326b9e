/**
 * Dynamic Hearts System - Player Manager
 * 
 * Handles all player-specific operations including heart management,
 * dynamic property storage, and player state tracking.
 * 
 * <AUTHOR> <PERSON><PERSON>13
 * @version 1.0.0
 */

import { Player, GameMode, EntityHealthComponent, EntityComponentTypes } from '@minecraft/server';
import type {
    PlayerHeartData,
    PlayerOperationResult,
    SystemError
} from './types.js';
import {
    SYSTEM_CONSTANTS,
    PROPERTY_KEYS,
    MESSAGES,
    ERROR_TYPES
} from './constants.js';

/**
 * Manages player-specific operations for the dynamic hearts system
 */
export class PlayerManager {
    private readonly playerCache: Map<string, PlayerHeartData> = new Map();
    private readonly debugMode: boolean = false;

    constructor(debugMode: boolean = false) {
        this.debugMode = debugMode;
    }

    /**
     * Initialize a player in the dynamic hearts system
     */
    public async initializePlayer(player: Player, isFirstJoin: boolean = false): Promise<PlayerOperationResult> {
        try {
            const playerId: string = player.id;
            
            // Check if player is already initialized
            const existingData: PlayerHeartData | null = await this.getPlayerData(player);
            
            if (existingData && existingData.initialized && !isFirstJoin) {
                return {
                    success: true,
                    data: existingData,
                    context: { reason: 'already_initialized' }
                };
            }

            // Determine initial heart count
            const initialHearts: number = isFirstJoin ? SYSTEM_CONSTANTS.MIN_HEARTS : 
                (existingData?.hearts ?? SYSTEM_CONSTANTS.MIN_HEARTS);

            // Create player data
            const playerData: PlayerHeartData = {
                playerId,
                hearts: initialHearts,
                initialized: true,
                lastHealth: initialHearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART,
                eliminated: false,
                lastUpdated: Date.now()
            };

            // Save to dynamic properties
            const saveResult: boolean = await this.savePlayerData(player, playerData);
            if (!saveResult) {
                throw new Error('Failed to save player data to dynamic properties');
            }

            // Update cache
            this.playerCache.set(playerId, playerData);

            // Set player health
            await this.setPlayerHealth(player, initialHearts);

            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} initialized with ${initialHearts} hearts`);
            }

            return {
                success: true,
                data: playerData,
                context: { 
                    reason: 'initialized',
                    isFirstJoin,
                    initialHearts
                }
            };

        } catch (error: unknown) {
            const systemError: SystemError = {
                type: ERROR_TYPES.INITIALIZATION_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now(),
                stack: error instanceof Error ? error.stack : undefined
            };

            return {
                success: false,
                error: systemError
            };
        }
    }

    /**
     * Get player heart data
     */
    public async getPlayerData(player: Player): Promise<PlayerHeartData | null> {
        try {
            const playerId: string = player.id;
            
            // Check cache first
            const cachedData: PlayerHeartData | undefined = this.playerCache.get(playerId);
            if (cachedData) {
                return cachedData;
            }

            // Load from dynamic properties
            const hearts: number = player.getDynamicProperty(PROPERTY_KEYS.PLAYER_HEARTS) as number ?? SYSTEM_CONSTANTS.MIN_HEARTS;
            const initialized: boolean = player.getDynamicProperty(PROPERTY_KEYS.PLAYER_INITIALIZED) as boolean ?? false;
            const lastHealth: number = player.getDynamicProperty(PROPERTY_KEYS.LAST_HEALTH) as number ?? SYSTEM_CONSTANTS.MIN_HEALTH;
            const eliminated: boolean = player.getDynamicProperty(PROPERTY_KEYS.ELIMINATED) as boolean ?? false;

            const playerData: PlayerHeartData = {
                playerId,
                hearts,
                initialized,
                lastHealth,
                eliminated,
                lastUpdated: Date.now()
            };

            // Update cache
            this.playerCache.set(playerId, playerData);

            return playerData;

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to get player data for ${player.name}: ${error}`);
            return null;
        }
    }

    /**
     * Save player heart data
     */
    public async savePlayerData(player: Player, data: PlayerHeartData): Promise<boolean> {
        try {
            // Save to dynamic properties
            player.setDynamicProperty(PROPERTY_KEYS.PLAYER_HEARTS, data.hearts);
            player.setDynamicProperty(PROPERTY_KEYS.PLAYER_INITIALIZED, data.initialized);
            player.setDynamicProperty(PROPERTY_KEYS.LAST_HEALTH, data.lastHealth);
            player.setDynamicProperty(PROPERTY_KEYS.ELIMINATED, data.eliminated);

            // Update cache
            this.playerCache.set(data.playerId, { ...data, lastUpdated: Date.now() });

            return true;

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to save player data for ${player.name}: ${error}`);
            return false;
        }
    }

    /**
     * Increase player hearts (called on death)
     */
    public async increasePlayerHearts(player: Player): Promise<PlayerOperationResult> {
        try {
            const playerData: PlayerHeartData | null = await this.getPlayerData(player);
            if (!playerData) {
                throw new Error('Player data not found');
            }

            const newHearts: number = Math.min(playerData.hearts + 1, SYSTEM_CONSTANTS.MAX_HEARTS);
            const wasAtMax: boolean = playerData.hearts >= SYSTEM_CONSTANTS.MAX_HEARTS;

            // Check if player should be eliminated
            if (wasAtMax) {
                return await this.eliminatePlayer(player);
            }

            // Update player data
            const updatedData: PlayerHeartData = {
                ...playerData,
                hearts: newHearts,
                lastHealth: newHearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART,
                lastUpdated: Date.now()
            };

            // Save data
            const saveResult: boolean = await this.savePlayerData(player, updatedData);
            if (!saveResult) {
                throw new Error('Failed to save updated player data');
            }

            // Send message to player
            const message: string = newHearts >= SYSTEM_CONSTANTS.MAX_HEARTS ? 
                MESSAGES.HEARTS.MAX_REACHED.replace('{hearts}', newHearts.toString()) :
                MESSAGES.HEARTS.GAINED.replace('{hearts}', newHearts.toString());
            
            player.sendMessage(message);

            return {
                success: true,
                data: updatedData,
                context: { 
                    previousHearts: playerData.hearts,
                    newHearts,
                    wasAtMax
                }
            };

        } catch (error: unknown) {
            const systemError: SystemError = {
                type: ERROR_TYPES.DYNAMIC_PROPERTY_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now()
            };

            return {
                success: false,
                error: systemError
            };
        }
    }

    /**
     * Eliminate player (switch to spectator mode)
     */
    public async eliminatePlayer(player: Player): Promise<PlayerOperationResult> {
        try {
            const playerData: PlayerHeartData | null = await this.getPlayerData(player);
            if (!playerData) {
                throw new Error('Player data not found');
            }

            // Update player data
            const updatedData: PlayerHeartData = {
                ...playerData,
                eliminated: true,
                lastUpdated: Date.now()
            };

            // Save data
            await this.savePlayerData(player, updatedData);

            // Switch to spectator mode
            player.setGameMode(GameMode.spectator);

            // Send elimination message
            player.sendMessage(MESSAGES.HEARTS.ELIMINATED);

            if (this.debugMode) {
                console.warn(`[Dynamic Hearts] Player ${player.name} eliminated and switched to spectator mode`);
            }

            return {
                success: true,
                data: updatedData,
                context: { reason: 'eliminated' }
            };

        } catch (error: unknown) {
            const systemError: SystemError = {
                type: ERROR_TYPES.GAMEMODE_CHANGE_ERROR,
                message: error instanceof Error ? error.message : String(error),
                playerId: player.id,
                timestamp: Date.now()
            };

            return {
                success: false,
                error: systemError
            };
        }
    }

    /**
     * Set player health to match their heart count
     */
    public async setPlayerHealth(player: Player, hearts: number): Promise<boolean> {
        try {
            const healthComponent: EntityHealthComponent | undefined = 
                player.getComponent(EntityComponentTypes.Health) as EntityHealthComponent;
            
            if (!healthComponent) {
                throw new Error('Health component not found');
            }

            const targetHealth: number = hearts * SYSTEM_CONSTANTS.HEALTH_PER_HEART;
            
            // Set max health first, then current health
            healthComponent.resetToMaxValue();
            
            // If we need less than max health, apply damage to reach target
            if (targetHealth < healthComponent.effectiveMax) {
                const damageToApply: number = healthComponent.effectiveMax - targetHealth;
                if (damageToApply > 0) {
                    player.applyDamage(damageToApply);
                }
            }

            return true;

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to set health for ${player.name}: ${error}`);
            return false;
        }
    }

    /**
     * Get current player health
     */
    public getPlayerHealth(player: Player): number | null {
        try {
            const healthComponent: EntityHealthComponent | undefined = 
                player.getComponent(EntityComponentTypes.Health) as EntityHealthComponent;
            
            return healthComponent?.currentValue ?? null;

        } catch (error: unknown) {
            console.error(`[Dynamic Hearts] Failed to get health for ${player.name}: ${error}`);
            return null;
        }
    }

    /**
     * Clear player from cache
     */
    public clearPlayerCache(playerId: string): void {
        this.playerCache.delete(playerId);
    }

    /**
     * Get all cached player data
     */
    public getAllCachedPlayers(): PlayerHeartData[] {
        return Array.from(this.playerCache.values());
    }

    /**
     * Clear all cached data
     */
    public clearAllCache(): void {
        this.playerCache.clear();
    }
}
