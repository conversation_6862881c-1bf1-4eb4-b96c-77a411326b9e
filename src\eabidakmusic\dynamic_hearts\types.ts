/**
 * Dynamic Hearts System - TypeScript Type Definitions
 * 
 * This file contains all TypeScript interfaces, types, and enums
 * used throughout the dynamic hearts system for type safety.
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0.0
 */

import type { Player } from '@minecraft/server';

/**
 * Player heart data interface
 */
export interface PlayerHeartData {
    /** Player's unique identifier */
    readonly playerId: string;
    
    /** Player's current heart count */
    hearts: number;
    
    /** Whether the player has been initialized */
    initialized: boolean;
    
    /** Player's last known health value */
    lastHealth: number;
    
    /** Whether the player has been eliminated */
    eliminated: boolean;
    
    /** Timestamp of last update */
    lastUpdated: number;
}

/**
 * System configuration interface
 */
export interface SystemConfig {
    /** Minimum hearts per player */
    readonly minHearts: number;
    
    /** Maximum hearts per player */
    readonly maxHearts: number;
    
    /** Health points per heart */
    readonly healthPerHeart: number;
    
    /** Health check interval in ticks */
    readonly healthCheckInterval: number;
    
    /** Whether debug mode is enabled */
    readonly debugMode: boolean;
}

/**
 * Health enforcement result interface
 */
export interface HealthEnforcementResult {
    /** The action that was taken */
    action: HealthEnforcementAction;
    
    /** Previous health value */
    previousHealth: number;
    
    /** New health value */
    newHealth: number;
    
    /** Whether the operation was successful */
    success: boolean;
    
    /** Error message if operation failed */
    error?: string;
}

/**
 * Player operation result interface
 */
export interface PlayerOperationResult {
    /** Whether the operation was successful */
    success: boolean;
    
    /** Result data if successful */
    data?: PlayerHeartData;
    
    /** Error information if failed */
    error?: SystemError;
    
    /** Additional context information */
    context?: Record<string, unknown>;
}

/**
 * System error interface
 */
export interface SystemError {
    /** Error type */
    type: SystemErrorType;
    
    /** Error message */
    message: string;
    
    /** Player ID if error is player-specific */
    playerId?: string;
    
    /** Timestamp when error occurred */
    timestamp: number;
    
    /** Stack trace if available */
    stack?: string;
    
    /** Additional error context */
    context?: Record<string, unknown>;
}

/**
 * System statistics interface
 */
export interface SystemStats {
    /** Total number of players tracked */
    totalPlayers: number;
    
    /** Number of active players */
    activePlayers: number;
    
    /** Number of eliminated players */
    eliminatedPlayers: number;
    
    /** Average hearts per player */
    averageHearts: number;
    
    /** System uptime in ticks */
    uptime: number;
    
    /** Number of errors encountered */
    errorCount: number;
    
    /** Last update timestamp */
    lastUpdate: number;
}

/**
 * Health monitoring data interface
 */
export interface HealthMonitorData {
    /** Player reference */
    player: Player;
    
    /** Current health value */
    currentHealth: number;
    
    /** Expected maximum health */
    expectedMaxHealth: number;
    
    /** Whether health is within limits */
    withinLimits: boolean;
    
    /** Recommended action */
    recommendedAction: HealthEnforcementAction;
}

/**
 * Event data interfaces
 */
export interface PlayerDeathEventData {
    /** Player who died */
    player: Player;
    
    /** Hearts before death */
    heartsBeforeDeath: number;
    
    /** Hearts after respawn */
    heartsAfterRespawn: number;
    
    /** Whether player was eliminated */
    eliminated: boolean;
    
    /** Timestamp of death */
    timestamp: number;
}

export interface PlayerSpawnEventData {
    /** Player who spawned */
    player: Player;
    
    /** Whether this is initial spawn */
    initialSpawn: boolean;
    
    /** Player's current heart count */
    hearts: number;
    
    /** Timestamp of spawn */
    timestamp: number;
}

/**
 * Health enforcement action types
 */
export enum HealthEnforcementAction {
    /** No action needed - health is within limits */
    NONE = 'none',
    
    /** Health was reduced to maximum allowed */
    REDUCED = 'reduced',
    
    /** Health was set to maximum allowed (from zero/invalid) */
    SET_TO_MAX = 'set_to_max',
    
    /** Player was switched to spectator mode */
    SPECTATOR_MODE = 'spectator_mode',
}

/**
 * System error types for better error handling
 */
export const SystemErrorType = {
    PLAYER_NOT_FOUND: 'player_not_found',
    HEALTH_COMPONENT_ERROR: 'health_component_error',
    DYNAMIC_PROPERTY_ERROR: 'dynamic_property_error',
    GAMEMODE_CHANGE_ERROR: 'gamemode_change_error',
    INITIALIZATION_ERROR: 'initialization_error',
    MONITORING_ERROR: 'monitoring_error',
} as const;

export type SystemErrorType = typeof SystemErrorType[keyof typeof SystemErrorType];

/**
 * Game mode type union
 */
export type GameModeType = 'survival' | 'creative' | 'adventure' | 'spectator';

/**
 * Property value types
 */
export type PropertyValue = string | number | boolean;

/**
 * Event handler function types
 */
export type PlayerDeathHandler = (data: PlayerDeathEventData) => void;
export type PlayerSpawnHandler = (data: PlayerSpawnEventData) => void;
export type ErrorHandler = (error: SystemError) => void;

/**
 * Utility type for making properties optional
 */
export type PartialPlayerHeartData = Partial<PlayerHeartData> & Pick<PlayerHeartData, 'playerId'>;

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
    /** Whether configuration is valid */
    valid: boolean;
    
    /** Validation errors if any */
    errors: string[];
    
    /** Warnings if any */
    warnings: string[];
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
    /** Average tick execution time in milliseconds */
    averageTickTime: number;
    
    /** Maximum tick execution time in milliseconds */
    maxTickTime: number;
    
    /** Number of slow operations detected */
    slowOperations: number;
    
    /** Memory usage statistics */
    memoryUsage: {
        /** Number of tracked players */
        trackedPlayers: number;
        
        /** Number of cached operations */
        cachedOperations: number;
    };
    
    /** Last performance check timestamp */
    lastCheck: number;
}
